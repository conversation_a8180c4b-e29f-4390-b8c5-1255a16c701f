import 'server-only'
import { Locale, locales, defaultLocale } from './types'

// Re-export types for server components
export type { Locale }
export { locales, defaultLocale }

const dictionaries = {
  zh: () => import('./dictionaries/zh.json').then((module) => module.default),
  ja: () => import('./dictionaries/ja.json').then((module) => module.default),
}

export const getDictionary = async (locale: Locale) => {
  if (!locales.includes(locale)) {
    return dictionaries[defaultLocale]()
  }
  return dictionaries[locale]()
}

export function isValidLocale(locale: string): locale is Locale {
  return locales.includes(locale as Locale)
}
