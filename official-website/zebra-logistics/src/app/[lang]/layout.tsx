import { Locale, locales, getDictionary } from '@/lib/i18n'
import { notFound } from 'next/navigation'
import Navigation from '@/components/Navigation'
import Footer from '@/components/Footer'

export async function generateStaticParams() {
  return locales.map((locale) => ({ lang: locale }))
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ lang: Locale }>
}) {
  const { lang } = await params
  const dict = await getDictionary(lang)
  
  return {
    title: lang === 'zh' 
      ? '斑马物巢 - 专业的福州-日本跨境物流服务'
      : 'ゼブラ物流 - 福州-日本間の専門的な越境物流サービス',
    description: lang === 'zh'
      ? '斑马物巢提供安全、快速、可靠的中日跨境物流解决方案，包括海运、空运、快递和清关服务。'
      : 'ゼブラ物流は安全で迅速、信頼性の高い中日越境物流ソリューションを提供し、海上輸送、航空輸送、宅配、通関サービスを含みます。',
  }
}

export default async function LangLayout({
  children,
  params,
}: {
  children: React.ReactNode
  params: Promise<{ lang: Locale }>
}) {
  const { lang } = await params
  
  if (!locales.includes(lang)) {
    notFound()
  }

  const dict = await getDictionary(lang)

  return (
    <html lang={lang}>
      <body>
        <Navigation dict={dict} lang={lang} />
        <main>{children}</main>
        <Footer dict={dict} lang={lang} />
      </body>
    </html>
  )
}
