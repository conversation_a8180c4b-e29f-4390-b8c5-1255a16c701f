import { Locale, getDictionary } from '@/lib/i18n'
import Hero from '@/components/Hero'
import Services from '@/components/Services'
import TrackingSection from '@/components/TrackingSection'
import StatsSection from '@/components/StatsSection'
import About from '@/components/About'
import Contact from '@/components/Contact'
import ScrollContainer from '@/components/ScrollContainer'

export default async function HomePage({
  params,
}: {
  params: Promise<{ lang: Locale }>
}) {
  const { lang } = await params
  const dict = await getDictionary(lang)

  const sectionIds = ['hero', 'services', 'tracking', 'stats', 'about', 'contact']

  return (
    <ScrollContainer sectionIds={sectionIds} lang={lang}>
      <Hero dict={dict} lang={lang} />
      <Services dict={dict} lang={lang} />
      <TrackingSection dict={dict} lang={lang} />
      <StatsSection dict={dict} lang={lang} />
      <About dict={dict} lang={lang} />
      <Contact dict={dict} lang={lang} />
    </ScrollContainer>
  )
}
