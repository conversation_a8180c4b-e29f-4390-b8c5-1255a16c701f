import { Locale, getDictionary } from '@/lib/i18n'
import Hero from '@/components/Hero'
import Services from '@/components/Services'
import About from '@/components/About'
import Contact from '@/components/Contact'

export default async function HomePage({
  params,
}: {
  params: Promise<{ lang: Locale }>
}) {
  const { lang } = await params
  const dict = await getDictionary(lang)

  return (
    <div className="min-h-screen">
      <Hero dict={dict} lang={lang} />
      <Services dict={dict} lang={lang} />
      <About dict={dict} lang={lang} />
      <Contact dict={dict} lang={lang} />
    </div>
  )
}
