'use client'

import { useState } from 'react'
import { Locale } from '@/lib/types'

interface TrackingSectionProps {
  dict: any
  lang: Locale
}

export default function TrackingSection({ dict, lang }: TrackingSectionProps) {
  const [trackingNumber, setTrackingNumber] = useState('')
  const [isTracking, setIsTracking] = useState(false)
  const [trackingResult, setTrackingResult] = useState<any>(null)

  const handleTracking = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!trackingNumber.trim()) return

    setIsTracking(true)
    
    // 模拟API调用
    setTimeout(() => {
      setTrackingResult({
        status: 'in_transit',
        location: lang === 'zh' ? '福州国际机场' : '福州国際空港',
        estimatedDelivery: '2024-01-15',
        updates: [
          {
            time: '2024-01-10 14:30',
            status: lang === 'zh' ? '已发货' : '発送済み',
            location: lang === 'zh' ? '福州仓库' : '福州倉庫'
          },
          {
            time: '2024-01-11 09:15',
            status: lang === 'zh' ? '运输中' : '輸送中',
            location: lang === 'zh' ? '福州国际机场' : '福州国際空港'
          },
          {
            time: '2024-01-12 16:45',
            status: lang === 'zh' ? '已到达日本' : '日本到着',
            location: lang === 'zh' ? '东京成田机场' : '東京成田空港'
          }
        ]
      })
      setIsTracking(false)
    }, 2000)
  }

  return (
    <section className="py-20 gradient-bg text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-responsive-lg font-bold mb-4">
            {dict.tracking?.title || (lang === 'zh' ? '货物追踪' : '貨物追跡')}
          </h2>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto">
            {dict.tracking?.subtitle || (lang === 'zh' ? '实时追踪您的货物状态，随时掌握物流信息' : 'リアルタイムで貨物の状況を追跡し、物流情報をいつでも把握')}
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
            <form onSubmit={handleTracking} className="mb-8">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <input
                    type="text"
                    value={trackingNumber}
                    onChange={(e) => setTrackingNumber(e.target.value)}
                    placeholder={lang === 'zh' ? '请输入运单号码' : '運送状番号を入力してください'}
                    className="w-full px-6 py-4 text-gray-900 bg-white rounded-lg border-0 focus:ring-2 focus:ring-orange-500 text-lg"
                  />
                </div>
                <button
                  type="submit"
                  disabled={isTracking || !trackingNumber.trim()}
                  className="btn-primary px-8 py-4 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center min-w-[120px]"
                >
                  {isTracking ? (
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
                  ) : (
                    <>
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                      {lang === 'zh' ? '查询' : '検索'}
                    </>
                  )}
                </button>
              </div>
            </form>

            {trackingResult && (
              <div className="bg-white rounded-xl p-6 text-gray-900 animate-fade-in-up">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-semibold">
                    {lang === 'zh' ? '追踪结果' : '追跡結果'}
                  </h3>
                  <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                    {lang === 'zh' ? '运输中' : '輸送中'}
                  </span>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <p className="text-sm text-gray-600 mb-1">
                      {lang === 'zh' ? '当前位置' : '現在位置'}
                    </p>
                    <p className="font-semibold">{trackingResult.location}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 mb-1">
                      {lang === 'zh' ? '预计送达' : '配達予定'}
                    </p>
                    <p className="font-semibold">{trackingResult.estimatedDelivery}</p>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-4">
                    {lang === 'zh' ? '物流轨迹' : '物流軌跡'}
                  </h4>
                  <div className="space-y-4">
                    {trackingResult.updates.map((update: any, index: number) => (
                      <div key={index} className="flex items-start space-x-4">
                        <div className="flex-shrink-0 w-3 h-3 bg-blue-500 rounded-full mt-2"></div>
                        <div className="flex-1">
                          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <p className="font-medium">{update.status}</p>
                            <p className="text-sm text-gray-500">{update.time}</p>
                          </div>
                          <p className="text-sm text-gray-600">{update.location}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* 快速入口 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 text-center hover:bg-white/20 transition-all duration-300 cursor-pointer">
              <div className="w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              <h3 className="font-semibold mb-2">
                {lang === 'zh' ? '在线报价' : 'オンライン見積もり'}
              </h3>
              <p className="text-sm text-blue-100">
                {lang === 'zh' ? '快速获取运费报价' : '運賃の見積もりを迅速に取得'}
              </p>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 text-center hover:bg-white/20 transition-all duration-300 cursor-pointer">
              <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <h3 className="font-semibold mb-2">
                {lang === 'zh' ? '网点查询' : '営業所検索'}
              </h3>
              <p className="text-sm text-blue-100">
                {lang === 'zh' ? '查找附近服务网点' : '近くのサービス拠点を検索'}
              </p>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 text-center hover:bg-white/20 transition-all duration-300 cursor-pointer">
              <div className="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="font-semibold mb-2">
                {lang === 'zh' ? '客服支持' : 'カスタマーサポート'}
              </h3>
              <p className="text-sm text-blue-100">
                {lang === 'zh' ? '24/7在线客服' : '24/7オンラインサポート'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
