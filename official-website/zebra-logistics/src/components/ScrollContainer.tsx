'use client'

import { useEffect, useState, useRef, ReactNode } from 'react'
import { Locale } from '@/lib/i18n'
import ScrollNavigation from './ScrollNavigation'

interface ScrollContainerProps {
  children: ReactNode[]
  sectionIds?: string[]
  lang: Locale
}

export default function ScrollContainer({ children, sectionIds = [], lang }: ScrollContainerProps) {
  const [currentSection, setCurrentSection] = useState(0)
  const [isScrolling, setIsScrolling] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const sectionsRef = useRef<HTMLDivElement[]>([])
  const scrollTimeoutRef = useRef<NodeJS.Timeout>()

  // 滚动到指定区块
  const scrollToSection = (index: number) => {
    if (index < 0 || index >= children.length || isScrolling) return
    
    const section = sectionsRef.current[index]
    if (section) {
      setIsScrolling(true)
      section.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
      })
      
      // 防止滚动过程中的重复触发
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }
      scrollTimeoutRef.current = setTimeout(() => {
        setIsScrolling(false)
      }, 1000)
    }
  }

  // 监听滚动事件，更新当前区块
  useEffect(() => {
    const handleScroll = () => {
      if (isScrolling) return

      const container = containerRef.current
      if (!container) return

      const sections = sectionsRef.current
      const scrollTop = container.scrollTop
      const containerHeight = container.clientHeight

      // 找到当前最接近顶部的区块
      let currentIndex = 0
      let minDistance = Infinity

      sections.forEach((section, index) => {
        if (section) {
          const sectionTop = section.offsetTop
          const distance = Math.abs(scrollTop - sectionTop)

          if (distance < minDistance) {
            minDistance = distance
            currentIndex = index
          }
        }
      })

      if (currentIndex !== currentSection) {
        setCurrentSection(currentIndex)
      }
    }

    const container = containerRef.current
    if (container) {
      container.addEventListener('scroll', handleScroll, { passive: true })
      return () => container.removeEventListener('scroll', handleScroll)
    }
  }, [currentSection, isScrolling])

  // 键盘导航
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'ArrowDown':
        case 'PageDown':
          e.preventDefault()
          scrollToSection(currentSection + 1)
          break
        case 'ArrowUp':
        case 'PageUp':
          e.preventDefault()
          scrollToSection(currentSection - 1)
          break
        case 'Home':
          e.preventDefault()
          scrollToSection(0)
          break
        case 'End':
          e.preventDefault()
          scrollToSection(children.length - 1)
          break
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [currentSection, children.length])

  // 鼠标滚轮控制
  useEffect(() => {
    const handleWheel = (e: WheelEvent) => {
      // 如果按住Shift键，使用传统滚动
      if (e.shiftKey) return

      e.preventDefault()
      
      if (isScrolling) return

      if (e.deltaY > 0) {
        // 向下滚动
        scrollToSection(currentSection + 1)
      } else {
        // 向上滚动
        scrollToSection(currentSection - 1)
      }
    }

    const container = containerRef.current
    if (container) {
      container.addEventListener('wheel', handleWheel, { passive: false })
      return () => container.removeEventListener('wheel', handleWheel)
    }
  }, [currentSection, isScrolling])

  return (
    <div className="relative">
      {/* 导航栏 */}
      <ScrollNavigation
        lang={lang}
        onSectionClick={scrollToSection}
        currentSection={currentSection}
      />

      {/* 滚动容器 */}
      <div
        ref={containerRef}
        className="scroll-container h-screen overflow-y-auto"
        style={{
          scrollSnapType: 'y mandatory',
          scrollBehavior: 'smooth'
        }}
      >
        {children.map((child, index) => (
          <div
            key={index}
            ref={(el) => {
              if (el) sectionsRef.current[index] = el
            }}
            className="scroll-section min-h-screen"
            style={{
              scrollSnapAlign: 'start',
              scrollSnapStop: 'always'
            }}
            id={sectionIds[index] || `section-${index}`}
          >
            {child}
          </div>
        ))}
      </div>

      {/* 页面指示器 */}
      <div className="fixed right-6 top-1/2 transform -translate-y-1/2 z-50 hidden md:block">
        <div className="flex flex-col space-y-3">
          {children.map((_, index) => (
            <button
              key={index}
              onClick={() => scrollToSection(index)}
              className={`w-3 h-3 rounded-full border-2 transition-all duration-300 hover:scale-125 ${
                currentSection === index
                  ? 'bg-blue-600 border-blue-600 shadow-lg'
                  : 'bg-transparent border-white/50 hover:border-blue-400'
              }`}
              aria-label={`跳转到第${index + 1}个区块`}
            />
          ))}
        </div>
      </div>

      {/* 滚动提示 */}
      {currentSection === 0 && (
        <div className="fixed bottom-8 left-1/2 transform -translate-x-1/2 z-40 animate-bounce">
          <div className="flex flex-col items-center text-white/80">
            <span className="text-sm mb-2">向下滚动探索更多</span>
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
          </div>
        </div>
      )}

      {/* 传统滚动提示 */}
      <div className="fixed bottom-4 right-4 z-40 text-xs text-gray-500 bg-black/20 backdrop-blur-sm rounded px-2 py-1">
        按住 Shift + 滚轮使用传统滚动
      </div>
    </div>
  )
}
