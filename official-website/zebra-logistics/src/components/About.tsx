import { Locale } from '@/lib/types'
import Image from 'next/image'

interface AboutProps {
  dict: any
  lang: Locale
}

export default function About({ dict, lang }: AboutProps) {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          <div>
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              {dict.about.title}
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              {dict.about.subtitle}
            </p>
            <p className="text-lg text-gray-700 leading-relaxed mb-8">
              {dict.about.description}
            </p>

            <div className="space-y-8">
              <div className="bg-blue-50 rounded-lg p-6">
                <h3 className="text-xl font-semibold text-blue-900 mb-3 flex items-center">
                  <svg className="w-6 h-6 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  {dict.about.mission}
                </h3>
                <p className="text-blue-800">
                  {dict.about.mission_text}
                </p>
              </div>

              <div className="bg-purple-50 rounded-lg p-6">
                <h3 className="text-xl font-semibold text-purple-900 mb-3 flex items-center">
                  <svg className="w-6 h-6 mr-3 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  {dict.about.vision}
                </h3>
                <p className="text-purple-800">
                  {dict.about.vision_text}
                </p>
              </div>
            </div>
          </div>

          <div className="relative">
            {/* Company Image */}
            <div className="relative h-80 rounded-2xl overflow-hidden mb-8 shadow-lg">
              <Image
                src="/images/logistics-warehouse.webp"
                alt={lang === 'zh' ? '斑马物巢现代物流仓库' : 'ゼブラ物流の現代物流倉庫'}
                fill
                className="object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div>
              <div className="absolute bottom-6 left-6 text-white">
                <h4 className="text-xl font-semibold mb-2">
                  {lang === 'zh' ? '现代化物流设施' : '現代化物流施設'}
                </h4>
                <p className="text-sm text-gray-200">
                  {lang === 'zh' ? '专业的仓储和配送中心' : '専門的な倉庫・配送センター'}
                </p>
              </div>
            </div>

            {/* Statistics Cards */}
            <div className="grid grid-cols-2 gap-6">
              <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl p-6 text-white">
                <div className="text-3xl font-bold mb-2">2020</div>
                <div className="text-blue-100">
                  {lang === 'zh' ? '成立年份' : '設立年'}
                </div>
              </div>

              <div className="bg-gradient-to-br from-green-500 to-green-600 rounded-xl p-6 text-white">
                <div className="text-3xl font-bold mb-2">1000+</div>
                <div className="text-green-100">
                  {lang === 'zh' ? '服务客户' : 'サービス顧客'}
                </div>
              </div>

              <div className="bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl p-6 text-white">
                <div className="text-3xl font-bold mb-2">50+</div>
                <div className="text-purple-100">
                  {lang === 'zh' ? '专业团队' : '専門チーム'}
                </div>
              </div>

              <div className="bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl p-6 text-white">
                <div className="text-3xl font-bold mb-2">99%</div>
                <div className="text-yellow-100">
                  {lang === 'zh' ? '客户满意度' : '顧客満足度'}
                </div>
              </div>
            </div>

            {/* Decorative Elements */}
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-blue-200 rounded-full opacity-20"></div>
            <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-purple-200 rounded-full opacity-20"></div>
          </div>
        </div>

        {/* Team Section */}
        <div className="mt-20">
          <h3 className="text-3xl font-bold text-gray-900 text-center mb-12">
            {lang === 'zh' ? '我们的优势' : '私たちの強み'}
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-3">
                {lang === 'zh' ? '地理优势' : '地理的優位性'}
              </h4>
              <p className="text-gray-600">
                {lang === 'zh' 
                  ? '位于福州，拥有得天独厚的地理位置，便于对日贸易往来'
                  : '福州に位置し、日本との貿易に有利な地理的位置を持つ'
                }
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-3">
                {lang === 'zh' ? '专业团队' : '専門チーム'}
              </h4>
              <p className="text-gray-600">
                {lang === 'zh'
                  ? '拥有丰富经验的专业团队，熟悉中日两国的贸易法规和流程'
                  : '豊富な経験を持つ専門チームが中日両国の貿易法規と流れを熟知'
                }
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-3">
                {lang === 'zh' ? '品质保证' : '品質保証'}
              </h4>
              <p className="text-gray-600">
                {lang === 'zh'
                  ? '严格的质量管理体系，确保每一票货物都能安全准时到达'
                  : '厳格な品質管理システムで、すべての貨物の安全で時間通りの到着を保証'
                }
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
