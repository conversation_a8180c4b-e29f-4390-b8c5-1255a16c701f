'use client'

import { useState, useEffect } from 'react'
import { Locale } from '@/lib/types'
import Image from 'next/image'

interface StatsSectionProps {
  dict: any
  lang: Locale
}

interface CounterProps {
  end: number
  duration: number
  suffix?: string
}

function Counter({ end, duration, suffix = '' }: CounterProps) {
  const [count, setCount] = useState(0)

  useEffect(() => {
    let startTime: number
    let animationFrame: number

    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime
      const progress = Math.min((currentTime - startTime) / duration, 1)
      
      setCount(Math.floor(progress * end))
      
      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate)
      }
    }

    animationFrame = requestAnimationFrame(animate)
    
    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame)
      }
    }
  }, [end, duration])

  return <span>{count}{suffix}</span>
}

export default function StatsSection({ dict, lang }: StatsSectionProps) {
  const stats = [
    {
      number: 2020,
      suffix: '',
      label: lang === 'zh' ? '成立年份' : '設立年',
      description: lang === 'zh' ? '专注跨境物流服务' : '越境物流サービスに特化',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      ),
      color: 'from-blue-500 to-blue-600'
    },
    {
      number: 1000,
      suffix: '+',
      label: lang === 'zh' ? '服务客户' : 'サービス顧客',
      description: lang === 'zh' ? '遍布中日两国的客户群体' : '中日両国に広がる顧客層',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      ),
      color: 'from-green-500 to-green-600'
    },
    {
      number: 50,
      suffix: '+',
      label: lang === 'zh' ? '专业团队' : '専門チーム',
      description: lang === 'zh' ? '经验丰富的物流专家' : '経験豊富な物流専門家',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
      ),
      color: 'from-purple-500 to-purple-600'
    },
    {
      number: 99,
      suffix: '%',
      label: lang === 'zh' ? '客户满意度' : '顧客満足度',
      description: lang === 'zh' ? '持续提供优质服务' : '継続的に優良サービスを提供',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
        </svg>
      ),
      color: 'from-orange-500 to-red-500'
    }
  ]

  return (
    <section className="py-20 bg-gray-50 relative overflow-hidden">
      {/* 背景图片 */}
      <div className="absolute inset-0 opacity-10">
        <Image
          src="/images/logistics-control-center.webp"
          alt="Logistics Control Center"
          fill
          className="object-cover"
        />
      </div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-responsive-lg font-bold text-gray-900 mb-4">
            {dict.stats?.title || (lang === 'zh' ? '数据说话' : 'データで語る')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {dict.stats?.subtitle || (lang === 'zh' ? '用数字见证我们的专业实力和服务品质' : '数字で私たちの専門力とサービス品質を証明')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <div
              key={index}
              className="card p-8 text-center group hover:scale-105 transition-all duration-300"
            >
              <div className={`w-16 h-16 bg-gradient-to-r ${stat.color} rounded-2xl flex items-center justify-center mx-auto mb-6 text-white group-hover:scale-110 transition-transform duration-300`}>
                {stat.icon}
              </div>
              
              <div className="mb-4">
                <div className="text-4xl font-bold text-gray-900 mb-2">
                  <Counter end={stat.number} duration={2000} suffix={stat.suffix} />
                </div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                  {stat.label}
                </h3>
                <p className="text-sm text-gray-600">
                  {stat.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* 成就展示 */}
        <div className="mt-20">
          <div className="text-center mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              {lang === 'zh' ? '我们的成就' : '私たちの実績'}
            </h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-100">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h4 className="text-lg font-semibold text-gray-900">
                  {lang === 'zh' ? '资质认证' : '資格認証'}
                </h4>
              </div>
              <p className="text-gray-600">
                {lang === 'zh' ? '获得国际货运代理资质，海关AEO认证企业' : '国際貨物輸送代理資格、税関AEO認証企業を取得'}
              </p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-100">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h4 className="text-lg font-semibold text-gray-900">
                  {lang === 'zh' ? '服务网络' : 'サービスネットワーク'}
                </h4>
              </div>
              <p className="text-gray-600">
                {lang === 'zh' ? '覆盖福州-日本主要城市的完整物流网络' : '福州-日本主要都市をカバーする完全な物流ネットワーク'}
              </p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-100">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h4 className="text-lg font-semibold text-gray-900">
                  {lang === 'zh' ? '时效保障' : '時間保証'}
                </h4>
              </div>
              <p className="text-gray-600">
                {lang === 'zh' ? '平均3-5个工作日送达，准时率达99%以上' : '平均3-5営業日で配達、定時率99%以上'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
