import { Locale } from '@/lib/types'
import Image from 'next/image'

interface ServicesProps {
  dict: any
  lang: Locale
}

export default function Services({ dict, lang }: ServicesProps) {
  const services = [
    {
      icon: (
        <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
        </svg>
      ),
      title: dict.services.air_freight.title,
      description: dict.services.air_freight.description,
      color: 'sky',
      image: '/images/cargo-aircraft-loading.webp',
      alt: lang === 'zh' ? '货机装载作业' : '貨物機積載作業'
    },
    {
      icon: (
        <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
        </svg>
      ),
      title: dict.services.express.title,
      description: dict.services.express.description,
      color: 'orange',
      image: '/images/express-delivery-service.webp',
      alt: lang === 'zh' ? '快递配送服务' : '宅配便サービス'
    },
    {
      icon: (
        <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      title: dict.services.customs.title,
      description: dict.services.customs.description,
      color: 'purple',
      image: '/images/modern-customs-office.webp',
      alt: lang === 'zh' ? '现代化海关办公室' : '現代的な税関オフィス'
    }
  ]

  const getColorClasses = (color: string) => {
    const colors = {
      sky: 'bg-sky-500 text-sky-500 bg-sky-50 hover:bg-sky-100',
      purple: 'bg-purple-500 text-purple-500 bg-purple-50 hover:bg-purple-100',
      orange: 'bg-orange-500 text-orange-500 bg-orange-50 hover:bg-orange-100',
      green: 'bg-green-500 text-green-500 bg-green-50 hover:bg-green-100'
    }
    return colors[color as keyof typeof colors] || colors.sky
  }

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            {dict.services.title}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {dict.services.subtitle}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {services.map((service, index) => {
            const colorClasses = getColorClasses(service.color).split(' ')
            return (
              <div
                key={index}
                className={`bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100`}
              >
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src={service.image}
                    alt={service.alt}
                    fill
                    className="object-cover transition-transform duration-300 hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                </div>

                <div className="p-6">
                  <div className={`w-16 h-16 ${colorClasses[2]} rounded-lg flex items-center justify-center mb-4 mx-auto`}>
                    <div className={`${colorClasses[1]}`}>
                      {service.icon}
                    </div>
                  </div>

                  <h3 className="text-xl font-semibold text-gray-900 mb-3 text-center">
                    {service.title}
                  </h3>

                  <p className="text-gray-600 text-center leading-relaxed">
                    {service.description}
                  </p>
                </div>
              </div>
            )
          })}
        </div>

        <div className="mt-16 text-center">
          <div className="bg-white rounded-2xl p-8 shadow-lg max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              {dict.services.why_choose.title}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-8">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">
                  {dict.services.why_choose.fast_efficient.title}
                </h4>
                <p className="text-gray-600 text-sm">
                  {dict.services.why_choose.fast_efficient.description}
                </p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">
                  {dict.services.why_choose.safe_reliable.title}
                </h4>
                <p className="text-gray-600 text-sm">
                  {dict.services.why_choose.safe_reliable.description}
                </p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.944a11.955 11.955 0 00-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">
                  {dict.services.why_choose.professional.title}
                </h4>
                <p className="text-gray-600 text-sm">
                  {dict.services.why_choose.professional.description}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
