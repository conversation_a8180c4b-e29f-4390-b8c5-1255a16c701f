(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__d8f3d5d7._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/src/lib/types.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "defaultLocale": (()=>defaultLocale),
    "isValidLocale": (()=>isValidLocale),
    "localeNames": (()=>localeNames),
    "locales": (()=>locales)
});
const locales = [
    'zh',
    'ja'
];
const defaultLocale = 'zh';
const localeNames = {
    zh: '中文',
    ja: '日本語'
};
function isValidLocale(locale) {
    return locales.includes(locale);
}
}}),
"[project]/src/lib/dictionaries/zh.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"nav\":{\"home\":\"首页\",\"services\":\"服务\",\"about\":\"关于我们\",\"contact\":\"联系我们\",\"language\":\"语言\"},\"hero\":{\"title\":\"斑马物巢\",\"subtitle\":\"专业的福州-日本跨境物流服务\",\"description\":\"为您提供安全、快速、可靠的中日跨境物流解决方案，让您的货物畅通无阻地到达目的地。\",\"cta\":\"立即咨询\",\"learn_more\":\"了解更多\"},\"services\":{\"title\":\"我们的服务\",\"subtitle\":\"专业的跨境物流解决方案\",\"sea_freight\":{\"title\":\"海运服务\",\"description\":\"经济实惠的海运方案，适合大批量货物运输\"},\"air_freight\":{\"title\":\"空运服务\",\"description\":\"快速便捷的空运服务，确保货物及时到达\"},\"express\":{\"title\":\"快递服务\",\"description\":\"门到门的快递服务，小件货物的最佳选择\"},\"customs\":{\"title\":\"清关服务\",\"description\":\"专业的清关代理，简化您的进出口流程\"}},\"about\":{\"title\":\"关于斑马物巢\",\"subtitle\":\"您值得信赖的物流合作伙伴\",\"description\":\"斑马物巢成立于2020年，专注于福州到日本的跨境物流服务。我们拥有专业的团队和丰富的经验，致力于为客户提供最优质的物流解决方案。\",\"mission\":\"我们的使命\",\"mission_text\":\"连接中日两国，为客户创造价值，推动跨境贸易的发展。\",\"vision\":\"我们的愿景\",\"vision_text\":\"成为中日跨境物流领域的领导者，为客户提供最专业、最可靠的服务。\"},\"contact\":{\"title\":\"联系我们\",\"subtitle\":\"随时为您提供专业咨询\",\"address\":\"地址\",\"address_text\":\"福建省福州市仓山区金山大道618号\",\"phone\":\"电话\",\"email\":\"邮箱\",\"wechat\":\"微信\",\"form\":{\"name\":\"姓名\",\"email\":\"邮箱\",\"phone\":\"电话\",\"company\":\"公司\",\"message\":\"留言\",\"submit\":\"提交\",\"required\":\"必填项\"}},\"footer\":{\"company\":\"斑马物巢物流有限公司\",\"copyright\":\"版权所有\",\"links\":{\"privacy\":\"隐私政策\",\"terms\":\"服务条款\"}},\"common\":{\"loading\":\"加载中...\",\"error\":\"出错了\",\"success\":\"成功\",\"back\":\"返回\",\"next\":\"下一步\",\"previous\":\"上一步\",\"close\":\"关闭\"}}"));}}),
"[project]/src/lib/dictionaries/ja.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"nav\":{\"home\":\"ホーム\",\"services\":\"サービス\",\"about\":\"会社概要\",\"contact\":\"お問い合わせ\",\"language\":\"言語\"},\"hero\":{\"title\":\"ゼブラ物流\",\"subtitle\":\"福州-日本間の専門的な越境物流サービス\",\"description\":\"安全で迅速、信頼性の高い中日越境物流ソリューションを提供し、お客様の貨物を目的地まで確実にお届けします。\",\"cta\":\"今すぐ相談\",\"learn_more\":\"詳細を見る\"},\"services\":{\"title\":\"私たちのサービス\",\"subtitle\":\"専門的な越境物流ソリューション\",\"sea_freight\":{\"title\":\"海上輸送サービス\",\"description\":\"経済的な海上輸送プラン、大量貨物輸送に最適\"},\"air_freight\":{\"title\":\"航空輸送サービス\",\"description\":\"迅速で便利な航空輸送サービス、貨物の確実な到着を保証\"},\"express\":{\"title\":\"宅配サービス\",\"description\":\"ドア・ツー・ドアの宅配サービス、小口貨物に最適\"},\"customs\":{\"title\":\"通関サービス\",\"description\":\"専門的な通関代行、輸出入手続きを簡素化\"}},\"about\":{\"title\":\"ゼブラ物流について\",\"subtitle\":\"信頼できる物流パートナー\",\"description\":\"ゼブラ物流は2020年に設立され、福州から日本への越境物流サービスに特化しています。専門チームと豊富な経験を持ち、お客様に最高品質の物流ソリューションを提供することに専念しています。\",\"mission\":\"私たちの使命\",\"mission_text\":\"中日両国を結び、お客様に価値を創造し、越境貿易の発展を推進します。\",\"vision\":\"私たちのビジョン\",\"vision_text\":\"中日越境物流分野のリーダーとなり、お客様に最も専門的で信頼性の高いサービスを提供します。\"},\"contact\":{\"title\":\"お問い合わせ\",\"subtitle\":\"いつでも専門的なご相談を承ります\",\"address\":\"住所\",\"address_text\":\"中国福建省福州市倉山区金山大道618号\",\"phone\":\"電話\",\"email\":\"メール\",\"wechat\":\"WeChat\",\"form\":{\"name\":\"お名前\",\"email\":\"メールアドレス\",\"phone\":\"電話番号\",\"company\":\"会社名\",\"message\":\"メッセージ\",\"submit\":\"送信\",\"required\":\"必須項目\"}},\"footer\":{\"company\":\"ゼブラ物流有限会社\",\"copyright\":\"著作権所有\",\"links\":{\"privacy\":\"プライバシーポリシー\",\"terms\":\"利用規約\"}},\"common\":{\"loading\":\"読み込み中...\",\"error\":\"エラーが発生しました\",\"success\":\"成功\",\"back\":\"戻る\",\"next\":\"次へ\",\"previous\":\"前へ\",\"close\":\"閉じる\"}}"));}}),
"[project]/src/lib/i18n.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getDictionary": (()=>getDictionary),
    "isValidLocale": (()=>isValidLocale)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$server$2d$only$2f$empty$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/server-only/empty.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/types.ts [middleware-edge] (ecmascript)");
;
;
const dictionaries = {
    zh: ()=>Promise.resolve().then(()=>__turbopack_context__.i("[project]/src/lib/dictionaries/zh.json (json)")).then((module)=>module.default),
    ja: ()=>Promise.resolve().then(()=>__turbopack_context__.i("[project]/src/lib/dictionaries/ja.json (json)")).then((module)=>module.default)
};
const getDictionary = async (locale)=>{
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["locales"].includes(locale)) {
        return dictionaries[__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["defaultLocale"]]();
    }
    return dictionaries[locale]();
};
function isValidLocale(locale) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["locales"].includes(locale);
}
}}),
"[project]/src/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "config": (()=>config),
    "middleware": (()=>middleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$i18n$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/i18n.ts [middleware-edge] (ecmascript)");
;
;
function middleware(request) {
    // Check if there is any supported locale in the pathname
    const pathname = request.nextUrl.pathname;
    // Skip middleware for static files and API routes
    if (pathname.startsWith('/_next') || pathname.startsWith('/api') || pathname.includes('.') || pathname.startsWith('/favicon.ico')) {
        return;
    }
    // Check if the pathname starts with a locale
    const pathnameIsMissingLocale = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$i18n$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["locales"].every((locale)=>!pathname.startsWith(`/${locale}/`) && pathname !== `/${locale}`);
    // Redirect if there is no locale
    if (pathnameIsMissingLocale) {
        // Get locale from Accept-Language header or use default
        const locale = getLocaleFromRequest(request) || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$i18n$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["defaultLocale"];
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL(`/${locale}${pathname.startsWith('/') ? '' : '/'}${pathname}`, request.url));
    }
}
function getLocaleFromRequest(request) {
    // Try to get locale from cookie first
    const cookieLocale = request.cookies.get('locale')?.value;
    if (cookieLocale && (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$i18n$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["isValidLocale"])(cookieLocale)) {
        return cookieLocale;
    }
    // Try to get locale from Accept-Language header
    const acceptLanguage = request.headers.get('accept-language');
    if (acceptLanguage) {
        // Simple language detection - in production you might want to use a library
        if (acceptLanguage.includes('ja')) return 'ja';
        if (acceptLanguage.includes('zh')) return 'zh';
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$i18n$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["defaultLocale"];
}
const config = {
    matcher: [
        // Skip all internal paths (_next)
        '/((?!_next|api|favicon.ico).*)'
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__d8f3d5d7._.js.map