{"version": 3, "sources": [], "sections": [{"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/i18n.ts"], "sourcesContent": ["import 'server-only'\n\nexport type Locale = 'zh' | 'ja'\n\nexport const locales: Locale[] = ['zh', 'ja']\nexport const defaultLocale: Locale = 'zh'\n\nexport const localeNames = {\n  zh: '中文',\n  ja: '日本語'\n}\n\nconst dictionaries = {\n  zh: () => import('./dictionaries/zh.json').then((module) => module.default),\n  ja: () => import('./dictionaries/ja.json').then((module) => module.default),\n}\n\nexport const getDictionary = async (locale: Locale) => {\n  if (!locales.includes(locale)) {\n    return dictionaries[defaultLocale]()\n  }\n  return dictionaries[locale]()\n}\n\nexport function isValidLocale(locale: string): locale is Locale {\n  return locales.includes(locale as Locale)\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAIO,MAAM,UAAoB;IAAC;IAAM;CAAK;AACtC,MAAM,gBAAwB;AAE9B,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;AACN;AAEA,MAAM,eAAe;IACnB,IAAI,IAAM,qGAAiC,IAAI,CAAC,CAAC,SAAW,OAAO,OAAO;IAC1E,IAAI,IAAM,qGAAiC,IAAI,CAAC,CAAC,SAAW,OAAO,OAAO;AAC5E;AAEO,MAAM,gBAAgB,OAAO;IAClC,IAAI,CAAC,QAAQ,QAAQ,CAAC,SAAS;QAC7B,OAAO,YAAY,CAAC,cAAc;IACpC;IACA,OAAO,YAAY,CAAC,OAAO;AAC7B;AAEO,SAAS,cAAc,MAAc;IAC1C,OAAO,QAAQ,QAAQ,CAAC;AAC1B"}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { locales, defaultLocale, isValidLocale } from './lib/i18n'\n\nexport function middleware(request: NextRequest) {\n  // Check if there is any supported locale in the pathname\n  const pathname = request.nextUrl.pathname\n  \n  // Skip middleware for static files and API routes\n  if (\n    pathname.startsWith('/_next') ||\n    pathname.startsWith('/api') ||\n    pathname.includes('.') ||\n    pathname.startsWith('/favicon.ico')\n  ) {\n    return\n  }\n\n  // Check if the pathname starts with a locale\n  const pathnameIsMissingLocale = locales.every(\n    (locale) => !pathname.startsWith(`/${locale}/`) && pathname !== `/${locale}`\n  )\n\n  // Redirect if there is no locale\n  if (pathnameIsMissingLocale) {\n    // Get locale from Accept-Language header or use default\n    const locale = getLocaleFromRequest(request) || defaultLocale\n    \n    return NextResponse.redirect(\n      new URL(`/${locale}${pathname.startsWith('/') ? '' : '/'}${pathname}`, request.url)\n    )\n  }\n}\n\nfunction getLocaleFromRequest(request: NextRequest): string | null {\n  // Try to get locale from cookie first\n  const cookieLocale = request.cookies.get('locale')?.value\n  if (cookieLocale && isValidLocale(cookieLocale)) {\n    return cookieLocale\n  }\n\n  // Try to get locale from Accept-Language header\n  const acceptLanguage = request.headers.get('accept-language')\n  if (acceptLanguage) {\n    // Simple language detection - in production you might want to use a library\n    if (acceptLanguage.includes('ja')) return 'ja'\n    if (acceptLanguage.includes('zh')) return 'zh'\n  }\n\n  return defaultLocale\n}\n\nexport const config = {\n  matcher: [\n    // Skip all internal paths (_next)\n    '/((?!_next|api|favicon.ico).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAEO,SAAS,WAAW,OAAoB;IAC7C,yDAAyD;IACzD,MAAM,WAAW,QAAQ,OAAO,CAAC,QAAQ;IAEzC,kDAAkD;IAClD,IACE,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,WACpB,SAAS,QAAQ,CAAC,QAClB,SAAS,UAAU,CAAC,iBACpB;QACA;IACF;IAEA,6CAA6C;IAC7C,MAAM,0BAA0B,0HAAA,CAAA,UAAO,CAAC,KAAK,CAC3C,CAAC,SAAW,CAAC,SAAS,UAAU,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,KAAK,aAAa,CAAC,CAAC,EAAE,QAAQ;IAG9E,iCAAiC;IACjC,IAAI,yBAAyB;QAC3B,wDAAwD;QACxD,MAAM,SAAS,qBAAqB,YAAY,0HAAA,CAAA,gBAAa;QAE7D,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,IAAI,IAAI,CAAC,CAAC,EAAE,SAAS,SAAS,UAAU,CAAC,OAAO,KAAK,MAAM,UAAU,EAAE,QAAQ,GAAG;IAEtF;AACF;AAEA,SAAS,qBAAqB,OAAoB;IAChD,sCAAsC;IACtC,MAAM,eAAe,QAAQ,OAAO,CAAC,GAAG,CAAC,WAAW;IACpD,IAAI,gBAAgB,CAAA,GAAA,0HAAA,CAAA,gBAAa,AAAD,EAAE,eAAe;QAC/C,OAAO;IACT;IAEA,gDAAgD;IAChD,MAAM,iBAAiB,QAAQ,OAAO,CAAC,GAAG,CAAC;IAC3C,IAAI,gBAAgB;QAClB,4EAA4E;QAC5E,IAAI,eAAe,QAAQ,CAAC,OAAO,OAAO;QAC1C,IAAI,eAAe,QAAQ,CAAC,OAAO,OAAO;IAC5C;IAEA,OAAO,0HAAA,CAAA,gBAAa;AACtB;AAEO,MAAM,SAAS;IACpB,SAAS;QACP,kCAAkC;QAClC;KACD;AACH"}}]}