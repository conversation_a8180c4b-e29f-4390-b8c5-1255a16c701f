module.exports = {

"[project]/src/lib/dictionaries/zh.json (json, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_lib_dictionaries_zh_json_017b8270._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/dictionaries/zh.json (json)");
    });
});
}}),
"[project]/src/lib/dictionaries/ja.json (json, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_lib_dictionaries_ja_json_44f27b9c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/dictionaries/ja.json (json)");
    });
});
}}),

};