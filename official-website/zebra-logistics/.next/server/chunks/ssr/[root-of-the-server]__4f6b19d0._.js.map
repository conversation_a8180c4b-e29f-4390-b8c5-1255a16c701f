{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/official-website/zebra-logistics/src/lib/types.ts"], "sourcesContent": ["export type Locale = 'zh' | 'ja'\n\nexport const locales: Locale[] = ['zh', 'ja']\nexport const defaultLocale: Locale = 'zh'\n\nexport const localeNames = {\n  zh: '中文',\n  ja: '日本語'\n}\n\nexport function isValidLocale(locale: string): locale is Locale {\n  return locales.includes(locale as Locale)\n}\n"], "names": [], "mappings": ";;;;;;AAEO,MAAM,UAAoB;IAAC;IAAM;CAAK;AACtC,MAAM,gBAAwB;AAE9B,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;AACN;AAEO,SAAS,cAAc,MAAc;IAC1C,OAAO,QAAQ,QAAQ,CAAC;AAC1B", "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/official-website/zebra-logistics/src/components/LanguageSwitcher.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { usePathname, useRouter } from 'next/navigation'\nimport { Locale, locales, localeNames } from '@/lib/types'\n\ninterface LanguageSwitcherProps {\n  currentLang: Locale\n}\n\nexport default function LanguageSwitcher({ currentLang }: LanguageSwitcherProps) {\n  const [isOpen, setIsOpen] = useState(false)\n  const pathname = usePathname()\n  const router = useRouter()\n\n  const switchLanguage = (newLang: Locale) => {\n    // Remove current language from pathname and add new language\n    const pathWithoutLang = pathname.replace(/^\\/[a-z]{2}/, '')\n    const newPath = `/${newLang}${pathWithoutLang}`\n    \n    // Set cookie to remember language preference\n    document.cookie = `locale=${newLang}; path=/; max-age=31536000` // 1 year\n    \n    router.push(newPath)\n    setIsOpen(false)\n  }\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md transition-colors\"\n      >\n        <svg\n          className=\"w-4 h-4 mr-1\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129\"\n          />\n        </svg>\n        {localeNames[currentLang]}\n        <svg\n          className={`w-4 h-4 ml-1 transition-transform ${isOpen ? 'rotate-180' : ''}`}\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n        >\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n        </svg>\n      </button>\n\n      {isOpen && (\n        <div className=\"absolute right-0 mt-2 w-32 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50\">\n          <div className=\"py-1\">\n            {locales.map((locale) => (\n              <button\n                key={locale}\n                onClick={() => switchLanguage(locale)}\n                className={`block w-full text-left px-4 py-2 text-sm transition-colors ${\n                  locale === currentLang\n                    ? 'bg-blue-50 text-blue-600'\n                    : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'\n                }`}\n              >\n                {localeNames[locale]}\n              </button>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUe,SAAS,iBAAiB,EAAE,WAAW,EAAyB;IAC7E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,iBAAiB,CAAC;QACtB,6DAA6D;QAC7D,MAAM,kBAAkB,SAAS,OAAO,CAAC,eAAe;QACxD,MAAM,UAAU,CAAC,CAAC,EAAE,UAAU,iBAAiB;QAE/C,6CAA6C;QAC7C,SAAS,MAAM,GAAG,CAAC,OAAO,EAAE,QAAQ,0BAA0B,CAAC,CAAC,SAAS;;QAEzE,OAAO,IAAI,CAAC;QACZ,UAAU;IACZ;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,SAAQ;wBACR,OAAM;kCAEN,cAAA,8OAAC;4BACC,eAAc;4BACd,gBAAe;4BACf,aAAa;4BACb,GAAE;;;;;;;;;;;oBAGL,mHAAA,CAAA,cAAW,CAAC,YAAY;kCACzB,8OAAC;wBACC,WAAW,CAAC,kCAAkC,EAAE,SAAS,eAAe,IAAI;wBAC5E,MAAK;wBACL,QAAO;wBACP,SAAQ;kCAER,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;YAIxE,wBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,mHAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,uBACZ,8OAAC;4BAEC,SAAS,IAAM,eAAe;4BAC9B,WAAW,CAAC,2DAA2D,EACrE,WAAW,cACP,6BACA,sDACJ;sCAED,mHAAA,CAAA,cAAW,CAAC,OAAO;2BARf;;;;;;;;;;;;;;;;;;;;;AAgBrB", "debugId": null}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/official-website/zebra-logistics/src/components/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { Locale } from '@/lib/types'\nimport LanguageSwitcher from './LanguageSwitcher'\n\ninterface NavigationProps {\n  dict: any\n  lang: Locale\n}\n\nexport default function Navigation({ dict, lang }: NavigationProps) {\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n  const pathname = usePathname()\n\n  const navItems = [\n    { href: `/${lang}`, label: dict.nav.home },\n    { href: `/${lang}/services`, label: dict.nav.services },\n    { href: `/${lang}/about`, label: dict.nav.about },\n    { href: `/${lang}/contact`, label: dict.nav.contact },\n  ]\n\n  return (\n    <nav className=\"bg-white shadow-lg sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link href={`/${lang}`} className=\"flex-shrink-0\">\n              <div className=\"flex items-center\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-lg\">Z</span>\n                </div>\n                <span className=\"ml-2 text-xl font-bold text-gray-900\">\n                  {lang === 'zh' ? '斑马物巢' : 'ゼブラ物流'}\n                </span>\n              </div>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${\n                  pathname === item.href\n                    ? 'text-blue-600 bg-blue-50'\n                    : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'\n                }`}\n              >\n                {item.label}\n              </Link>\n            ))}\n            <LanguageSwitcher currentLang={lang} />\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden flex items-center\">\n            <LanguageSwitcher currentLang={lang} />\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"ml-2 inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100\"\n            >\n              <span className=\"sr-only\">Open main menu</span>\n              <svg\n                className={`${isMenuOpen ? 'hidden' : 'block'} h-6 w-6`}\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n              >\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              </svg>\n              <svg\n                className={`${isMenuOpen ? 'block' : 'hidden'} h-6 w-6`}\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n              >\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      {isMenuOpen && (\n        <div className=\"md:hidden\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t\">\n            {navItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className={`block px-3 py-2 rounded-md text-base font-medium transition-colors ${\n                  pathname === item.href\n                    ? 'text-blue-600 bg-blue-50'\n                    : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'\n                }`}\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {item.label}\n              </Link>\n            ))}\n          </div>\n        </div>\n      )}\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAae,SAAS,WAAW,EAAE,IAAI,EAAE,IAAI,EAAmB;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW;QACf;YAAE,MAAM,CAAC,CAAC,EAAE,MAAM;YAAE,OAAO,KAAK,GAAG,CAAC,IAAI;QAAC;QACzC;YAAE,MAAM,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC;YAAE,OAAO,KAAK,GAAG,CAAC,QAAQ;QAAC;QACtD;YAAE,MAAM,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC;YAAE,OAAO,KAAK,GAAG,CAAC,KAAK;QAAC;QAChD;YAAE,MAAM,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC;YAAE,OAAO,KAAK,GAAG,CAAC,OAAO;QAAC;KACrD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,CAAC,EAAE,MAAM;gCAAE,WAAU;0CAChC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDACb,SAAS,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;sCAOlC,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,2DAA2D,EACrE,aAAa,KAAK,IAAI,GAClB,6BACA,sDACJ;kDAED,KAAK,KAAK;uCARN,KAAK,IAAI;;;;;8CAWlB,8OAAC,sIAAA,CAAA,UAAgB;oCAAC,aAAa;;;;;;;;;;;;sCAIjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sIAAA,CAAA,UAAgB;oCAAC,aAAa;;;;;;8CAC/B,8OAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,8OAAC;4CACC,WAAW,GAAG,aAAa,WAAW,QAAQ,QAAQ,CAAC;4CACvD,OAAM;4CACN,MAAK;4CACL,SAAQ;4CACR,QAAO;sDAEP,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;sDAEvE,8OAAC;4CACC,WAAW,GAAG,aAAa,UAAU,SAAS,QAAQ,CAAC;4CACvD,OAAM;4CACN,MAAK;4CACL,SAAQ;4CACR,QAAO;sDAEP,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ9E,4BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAW,CAAC,mEAAmE,EAC7E,aAAa,KAAK,IAAI,GAClB,6BACA,sDACJ;4BACF,SAAS,IAAM,cAAc;sCAE5B,KAAK,KAAK;2BATN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;AAiB9B", "debugId": null}}]}