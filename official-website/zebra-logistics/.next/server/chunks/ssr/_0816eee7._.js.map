{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/official-website/zebra-logistics/src/components/Hero.tsx"], "sourcesContent": ["import Link from 'next/link'\nimport Image from 'next/image'\nimport { Locale } from '@/lib/types'\n\ninterface HeroProps {\n  dict: any\n  lang: Locale\n}\n\nexport default function Hero({ dict, lang }: HeroProps) {\n  return (\n    <section className=\"relative gradient-bg text-white overflow-hidden min-h-screen flex items-center\">\n      {/* Background Image */}\n      <div className=\"absolute inset-0\">\n        <Image\n          src=\"/images/hero-logistics-hub.webp\"\n          alt={lang === 'zh' ? '现代国际物流枢纽' : '現代國際物流樞紐'}\n          fill\n          className=\"object-cover opacity-30\"\n          priority\n        />\n        <div className=\"absolute inset-0 bg-gradient-to-br from-blue-900/90 via-blue-800/85 to-purple-900/90\"></div>\n      </div>\n      \n      {/* Content */}\n      <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32 w-full\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          <div className=\"text-center lg:text-left animate-fade-in-left\">\n            <h1 className=\"text-responsive-xl font-bold leading-tight mb-6\">\n              <span className=\"block\">{dict.hero.title}</span>\n              <span className=\"block gradient-text text-responsive-lg mt-2\">\n                {dict.hero.subtitle}\n              </span>\n            </h1>\n\n            <p className=\"text-responsive-md text-blue-100 mb-8 leading-relaxed\">\n              {dict.hero.description}\n            </p>\n\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\">\n              <Link\n                href={`/${lang}/contact`}\n                className=\"btn-primary inline-flex items-center justify-center text-lg font-semibold\"\n              >\n                {dict.hero.cta}\n                <svg className=\"ml-2 w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 7l5 5m0 0l-5 5m5-5H6\" />\n                </svg>\n              </Link>\n              \n              <Link\n                href={`/${lang}/services`}\n                className=\"btn-secondary inline-flex items-center justify-center text-lg font-semibold\"\n              >\n                {dict.hero.learn_more}\n              </Link>\n            </div>\n          </div>\n          \n          <div className=\"relative\">\n            {/* Placeholder for logistics illustration */}\n            <div className=\"relative bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20\">\n              <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-6\">\n                <div className=\"bg-sky-500/30 rounded-lg p-6 text-center\">\n                  <div className=\"w-16 h-16 bg-white/20 rounded-lg mx-auto mb-4 flex items-center justify-center\">\n                    <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\n                    </svg>\n                  </div>\n                  <p className=\"text-lg font-semibold\">{lang === 'zh' ? '空运服务' : '航空輸送'}</p>\n                  <p className=\"text-sm text-blue-200 mt-2\">{lang === 'zh' ? '快速便捷' : '迅速便利'}</p>\n                </div>\n\n                <div className=\"bg-green-500/30 rounded-lg p-6 text-center\">\n                  <div className=\"w-16 h-16 bg-white/20 rounded-lg mx-auto mb-4 flex items-center justify-center\">\n                    <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\n                    </svg>\n                  </div>\n                  <p className=\"text-lg font-semibold\">{lang === 'zh' ? '快递服务' : '宅配サービス'}</p>\n                  <p className=\"text-sm text-green-200 mt-2\">{lang === 'zh' ? '门到门' : 'ドア・ツー・ドア'}</p>\n                </div>\n\n                <div className=\"bg-purple-500/30 rounded-lg p-6 text-center\">\n                  <div className=\"w-16 h-16 bg-white/20 rounded-lg mx-auto mb-4 flex items-center justify-center\">\n                    <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                  </div>\n                  <p className=\"text-lg font-semibold\">{lang === 'zh' ? '清关服务' : '通関サービス'}</p>\n                  <p className=\"text-sm text-purple-200 mt-2\">{lang === 'zh' ? '专业代理' : '専門代行'}</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQe,SAAS,KAAK,EAAE,IAAI,EAAE,IAAI,EAAa;IACpD,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAK,SAAS,OAAO,aAAa;wBAClC,IAAI;wBACJ,WAAU;wBACV,QAAQ;;;;;;kCAEV,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAK,WAAU;sDAAS,KAAK,IAAI,CAAC,KAAK;;;;;;sDACxC,8OAAC;4CAAK,WAAU;sDACb,KAAK,IAAI,CAAC,QAAQ;;;;;;;;;;;;8CAIvB,8OAAC;oCAAE,WAAU;8CACV,KAAK,IAAI,CAAC,WAAW;;;;;;8CAGxB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC;4CACxB,WAAU;;gDAET,KAAK,IAAI,CAAC,GAAG;8DACd,8OAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACtE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;sDAIzE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC;4CACzB,WAAU;sDAET,KAAK,IAAI,CAAC,UAAU;;;;;;;;;;;;;;;;;;sCAK3B,8OAAC;4BAAI,WAAU;sCAEb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACjE,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,8OAAC;oDAAE,WAAU;8DAAyB,SAAS,OAAO,SAAS;;;;;;8DAC/D,8OAAC;oDAAE,WAAU;8DAA8B,SAAS,OAAO,SAAS;;;;;;;;;;;;sDAGtE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACjE,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,8OAAC;oDAAE,WAAU;8DAAyB,SAAS,OAAO,SAAS;;;;;;8DAC/D,8OAAC;oDAAE,WAAU;8DAA+B,SAAS,OAAO,QAAQ;;;;;;;;;;;;sDAGtE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACjE,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,8OAAC;oDAAE,WAAU;8DAAyB,SAAS,OAAO,SAAS;;;;;;8DAC/D,8OAAC;oDAAE,WAAU;8DAAgC,SAAS,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxF", "debugId": null}}, {"offset": {"line": 369, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/official-website/zebra-logistics/src/components/Services.tsx"], "sourcesContent": ["import { Locale } from '@/lib/types'\nimport Image from 'next/image'\n\ninterface ServicesProps {\n  dict: any\n  lang: Locale\n}\n\nexport default function Services({ dict, lang }: ServicesProps) {\n  const services = [\n    {\n      icon: (\n        <svg className=\"w-12 h-12\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\n        </svg>\n      ),\n      title: dict.services.air_freight.title,\n      description: dict.services.air_freight.description,\n      color: 'sky',\n      image: '/images/cargo-aircraft-loading.webp',\n      alt: lang === 'zh' ? '货机装载作业' : '貨物機積載作業'\n    },\n    {\n      icon: (\n        <svg className=\"w-12 h-12\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\n        </svg>\n      ),\n      title: dict.services.express.title,\n      description: dict.services.express.description,\n      color: 'orange',\n      image: '/images/express-delivery-service.webp',\n      alt: lang === 'zh' ? '快递配送服务' : '宅配便サービス'\n    },\n    {\n      icon: (\n        <svg className=\"w-12 h-12\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n        </svg>\n      ),\n      title: dict.services.customs.title,\n      description: dict.services.customs.description,\n      color: 'purple',\n      image: '/images/modern-customs-office.webp',\n      alt: lang === 'zh' ? '现代化海关办公室' : '現代的な税関オフィス'\n    }\n  ]\n\n  const getColorClasses = (color: string) => {\n    const colors = {\n      sky: 'bg-sky-500 text-sky-500 bg-sky-50 hover:bg-sky-100',\n      purple: 'bg-purple-500 text-purple-500 bg-purple-50 hover:bg-purple-100',\n      orange: 'bg-orange-500 text-orange-500 bg-orange-50 hover:bg-orange-100',\n      green: 'bg-green-500 text-green-500 bg-green-50 hover:bg-green-100'\n    }\n    return colors[color as keyof typeof colors] || colors.sky\n  }\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl font-bold text-gray-900 mb-4\">\n            {dict.services.title}\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            {dict.services.subtitle}\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto\">\n          {services.map((service, index) => {\n            const colorClasses = getColorClasses(service.color).split(' ')\n            return (\n              <div\n                key={index}\n                className={`bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100`}\n              >\n                <div className=\"relative h-48 overflow-hidden\">\n                  <Image\n                    src={service.image}\n                    alt={service.alt}\n                    fill\n                    className=\"object-cover transition-transform duration-300 hover:scale-105\"\n                  />\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\" />\n                </div>\n\n                <div className=\"p-6\">\n                  <div className={`w-16 h-16 ${colorClasses[2]} rounded-lg flex items-center justify-center mb-4 mx-auto`}>\n                    <div className={`${colorClasses[1]}`}>\n                      {service.icon}\n                    </div>\n                  </div>\n\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-3 text-center\">\n                    {service.title}\n                  </h3>\n\n                  <p className=\"text-gray-600 text-center leading-relaxed\">\n                    {service.description}\n                  </p>\n                </div>\n              </div>\n            )\n          })}\n        </div>\n\n        <div className=\"mt-16 text-center\">\n          <div className=\"bg-white rounded-2xl p-8 shadow-lg max-w-4xl mx-auto\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              {dict.services.why_choose.title}\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mt-8\">\n              <div className=\"text-center\">\n                <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                  <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                  </svg>\n                </div>\n                <h4 className=\"font-semibold text-gray-900 mb-2\">\n                  {dict.services.why_choose.fast_efficient.title}\n                </h4>\n                <p className=\"text-gray-600 text-sm\">\n                  {dict.services.why_choose.fast_efficient.description}\n                </p>\n              </div>\n\n              <div className=\"text-center\">\n                <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                  <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n                  </svg>\n                </div>\n                <h4 className=\"font-semibold text-gray-900 mb-2\">\n                  {dict.services.why_choose.safe_reliable.title}\n                </h4>\n                <p className=\"text-gray-600 text-sm\">\n                  {dict.services.why_choose.safe_reliable.description}\n                </p>\n              </div>\n\n              <div className=\"text-center\">\n                <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                  <svg className=\"w-6 h-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.944a11.955 11.955 0 00-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n                  </svg>\n                </div>\n                <h4 className=\"font-semibold text-gray-900 mb-2\">\n                  {dict.services.why_choose.professional.title}\n                </h4>\n                <p className=\"text-gray-600 text-sm\">\n                  {dict.services.why_choose.professional.description}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAOe,SAAS,SAAS,EAAE,IAAI,EAAE,IAAI,EAAiB;IAC5D,MAAM,WAAW;QACf;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAY,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACnE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO,KAAK,QAAQ,CAAC,WAAW,CAAC,KAAK;YACtC,aAAa,KAAK,QAAQ,CAAC,WAAW,CAAC,WAAW;YAClD,OAAO;YACP,OAAO;YACP,KAAK,SAAS,OAAO,WAAW;QAClC;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAY,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACnE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO,KAAK,QAAQ,CAAC,OAAO,CAAC,KAAK;YAClC,aAAa,KAAK,QAAQ,CAAC,OAAO,CAAC,WAAW;YAC9C,OAAO;YACP,OAAO;YACP,KAAK,SAAS,OAAO,WAAW;QAClC;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAY,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACnE,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO,KAAK,QAAQ,CAAC,OAAO,CAAC,KAAK;YAClC,aAAa,KAAK,QAAQ,CAAC,OAAO,CAAC,WAAW;YAC9C,OAAO;YACP,OAAO;YACP,KAAK,SAAS,OAAO,aAAa;QACpC;KACD;IAED,MAAM,kBAAkB,CAAC;QACvB,MAAM,SAAS;YACb,KAAK;YACL,QAAQ;YACR,QAAQ;YACR,OAAO;QACT;QACA,OAAO,MAAM,CAAC,MAA6B,IAAI,OAAO,GAAG;IAC3D;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,KAAK,QAAQ,CAAC,KAAK;;;;;;sCAEtB,8OAAC;4BAAE,WAAU;sCACV,KAAK,QAAQ,CAAC,QAAQ;;;;;;;;;;;;8BAI3B,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS;wBACtB,MAAM,eAAe,gBAAgB,QAAQ,KAAK,EAAE,KAAK,CAAC;wBAC1D,qBACE,8OAAC;4BAEC,WAAW,CAAC,+IAA+I,CAAC;;8CAE5J,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK,QAAQ,KAAK;4CAClB,KAAK,QAAQ,GAAG;4CAChB,IAAI;4CACJ,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAGjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,UAAU,EAAE,YAAY,CAAC,EAAE,CAAC,yDAAyD,CAAC;sDACrG,cAAA,8OAAC;gDAAI,WAAW,GAAG,YAAY,CAAC,EAAE,EAAE;0DACjC,QAAQ,IAAI;;;;;;;;;;;sDAIjB,8OAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAGhB,8OAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;;;;;;;;2BAzBnB;;;;;oBA8BX;;;;;;8BAGF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,KAAK,QAAQ,CAAC,UAAU,CAAC,KAAK;;;;;;0CAEjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC/E,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,8OAAC;gDAAG,WAAU;0DACX,KAAK,QAAQ,CAAC,UAAU,CAAC,cAAc,CAAC,KAAK;;;;;;0DAEhD,8OAAC;gDAAE,WAAU;0DACV,KAAK,QAAQ,CAAC,UAAU,CAAC,cAAc,CAAC,WAAW;;;;;;;;;;;;kDAIxD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAyB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAChF,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,8OAAC;gDAAG,WAAU;0DACX,KAAK,QAAQ,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK;;;;;;0DAE/C,8OAAC;gDAAE,WAAU;0DACV,KAAK,QAAQ,CAAC,UAAU,CAAC,aAAa,CAAC,WAAW;;;;;;;;;;;;kDAIvD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAA0B,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjF,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,8OAAC;gDAAG,WAAU;0DACX,KAAK,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,KAAK;;;;;;0DAE9C,8OAAC;gDAAE,WAAU;0DACV,KAAK,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpE", "debugId": null}}, {"offset": {"line": 791, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/official-website/zebra-logistics/src/components/TrackingSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/TrackingSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/TrackingSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "debugId": null}}, {"offset": {"line": 805, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/official-website/zebra-logistics/src/components/TrackingSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/TrackingSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/TrackingSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "debugId": null}}, {"offset": {"line": 819, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 829, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/official-website/zebra-logistics/src/components/StatsSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/StatsSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/StatsSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmS,GAChU,iEACA", "debugId": null}}, {"offset": {"line": 843, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/official-website/zebra-logistics/src/components/StatsSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/StatsSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/StatsSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+Q,GAC5S,6CACA", "debugId": null}}, {"offset": {"line": 857, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 867, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/official-website/zebra-logistics/src/components/About.tsx"], "sourcesContent": ["import { Locale } from '@/lib/types'\nimport Image from 'next/image'\n\ninterface AboutProps {\n  dict: any\n  lang: Locale\n}\n\nexport default function About({ dict, lang }: AboutProps) {\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\">\n          <div>\n            <h2 className=\"text-4xl font-bold text-gray-900 mb-6\">\n              {dict.about.title}\n            </h2>\n            <p className=\"text-xl text-gray-600 mb-8\">\n              {dict.about.subtitle}\n            </p>\n            <p className=\"text-lg text-gray-700 leading-relaxed mb-8\">\n              {dict.about.description}\n            </p>\n\n            <div className=\"space-y-8\">\n              <div className=\"bg-blue-50 rounded-lg p-6\">\n                <h3 className=\"text-xl font-semibold text-blue-900 mb-3 flex items-center\">\n                  <svg className=\"w-6 h-6 mr-3 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                  </svg>\n                  {dict.about.mission}\n                </h3>\n                <p className=\"text-blue-800\">\n                  {dict.about.mission_text}\n                </p>\n              </div>\n\n              <div className=\"bg-purple-50 rounded-lg p-6\">\n                <h3 className=\"text-xl font-semibold text-purple-900 mb-3 flex items-center\">\n                  <svg className=\"w-6 h-6 mr-3 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n                  </svg>\n                  {dict.about.vision}\n                </h3>\n                <p className=\"text-purple-800\">\n                  {dict.about.vision_text}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"relative\">\n            {/* Company Image */}\n            <div className=\"relative h-80 rounded-2xl overflow-hidden mb-8 shadow-lg card\">\n              <Image\n                src=\"/images/professional-team.webp\"\n                alt={lang === 'zh' ? '斑马物巢专业团队' : 'ゼブラ物流の専門チーム'}\n                fill\n                className=\"object-cover\"\n              />\n              <div className=\"absolute inset-0 bg-gradient-to-t from-black/40 to-transparent\"></div>\n              <div className=\"absolute bottom-6 left-6 text-white\">\n                <h4 className=\"text-xl font-semibold mb-2\">\n                  {lang === 'zh' ? '专业的国际物流团队' : '専門的な国際物流チーム'}\n                </h4>\n                <p className=\"text-sm text-gray-200\">\n                  {lang === 'zh' ? '经验丰富的跨境物流专家' : '経験豊富な越境物流専門家'}\n                </p>\n              </div>\n            </div>\n\n            {/* Statistics Cards */}\n            <div className=\"grid grid-cols-2 gap-6\">\n              <div className=\"bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl p-6 text-white\">\n                <div className=\"text-3xl font-bold mb-2\">2020</div>\n                <div className=\"text-blue-100\">\n                  {lang === 'zh' ? '成立年份' : '設立年'}\n                </div>\n              </div>\n\n              <div className=\"bg-gradient-to-br from-green-500 to-green-600 rounded-xl p-6 text-white\">\n                <div className=\"text-3xl font-bold mb-2\">1000+</div>\n                <div className=\"text-green-100\">\n                  {lang === 'zh' ? '服务客户' : 'サービス顧客'}\n                </div>\n              </div>\n\n              <div className=\"bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl p-6 text-white\">\n                <div className=\"text-3xl font-bold mb-2\">50+</div>\n                <div className=\"text-purple-100\">\n                  {lang === 'zh' ? '专业团队' : '専門チーム'}\n                </div>\n              </div>\n\n              <div className=\"bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl p-6 text-white\">\n                <div className=\"text-3xl font-bold mb-2\">99%</div>\n                <div className=\"text-yellow-100\">\n                  {lang === 'zh' ? '客户满意度' : '顧客満足度'}\n                </div>\n              </div>\n            </div>\n\n            {/* Decorative Elements */}\n            <div className=\"absolute -top-4 -right-4 w-24 h-24 bg-blue-200 rounded-full opacity-20\"></div>\n            <div className=\"absolute -bottom-4 -left-4 w-32 h-32 bg-purple-200 rounded-full opacity-20\"></div>\n          </div>\n        </div>\n\n        {/* Team Section */}\n        <div className=\"mt-20\">\n          <h3 className=\"text-3xl font-bold text-gray-900 text-center mb-12\">\n            {lang === 'zh' ? '我们的优势' : '私たちの強み'}\n          </h3>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-8 h-8 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                </svg>\n              </div>\n              <h4 className=\"text-xl font-semibold text-gray-900 mb-3\">\n                {lang === 'zh' ? '地理优势' : '地理的優位性'}\n              </h4>\n              <p className=\"text-gray-600\">\n                {lang === 'zh' \n                  ? '位于福州，拥有得天独厚的地理位置，便于对日贸易往来'\n                  : '福州に位置し、日本との貿易に有利な地理的位置を持つ'\n                }\n              </p>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-8 h-8 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\" />\n                </svg>\n              </div>\n              <h4 className=\"text-xl font-semibold text-gray-900 mb-3\">\n                {lang === 'zh' ? '专业团队' : '専門チーム'}\n              </h4>\n              <p className=\"text-gray-600\">\n                {lang === 'zh'\n                  ? '拥有丰富经验的专业团队，熟悉中日两国的贸易法规和流程'\n                  : '豊富な経験を持つ専門チームが中日両国の貿易法規と流れを熟知'\n                }\n              </p>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-8 h-8 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n                </svg>\n              </div>\n              <h4 className=\"text-xl font-semibold text-gray-900 mb-3\">\n                {lang === 'zh' ? '品质保证' : '品質保証'}\n              </h4>\n              <p className=\"text-gray-600\">\n                {lang === 'zh'\n                  ? '严格的质量管理体系，确保每一票货物都能安全准时到达'\n                  : '厳格な品質管理システムで、すべての貨物の安全で時間通りの到着を保証'\n                }\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAOe,SAAS,MAAM,EAAE,IAAI,EAAE,IAAI,EAAc;IACtD,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CACX,KAAK,KAAK,CAAC,KAAK;;;;;;8CAEnB,8OAAC;oCAAE,WAAU;8CACV,KAAK,KAAK,CAAC,QAAQ;;;;;;8CAEtB,8OAAC;oCAAE,WAAU;8CACV,KAAK,KAAK,CAAC,WAAW;;;;;;8CAGzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAI,WAAU;4DAA6B,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACpF,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;wDAEtE,KAAK,KAAK,CAAC,OAAO;;;;;;;8DAErB,8OAAC;oDAAE,WAAU;8DACV,KAAK,KAAK,CAAC,YAAY;;;;;;;;;;;;sDAI5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAI,WAAU;4DAA+B,MAAK;4DAAO,QAAO;4DAAe,SAAQ;;8EACtF,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;8EACrE,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;wDAEtE,KAAK,KAAK,CAAC,MAAM;;;;;;;8DAEpB,8OAAC;oDAAE,WAAU;8DACV,KAAK,KAAK,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;;sCAM/B,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAK,SAAS,OAAO,aAAa;4CAClC,IAAI;4CACJ,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,SAAS,OAAO,cAAc;;;;;;8DAEjC,8OAAC;oDAAE,WAAU;8DACV,SAAS,OAAO,gBAAgB;;;;;;;;;;;;;;;;;;8CAMvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAA0B;;;;;;8DACzC,8OAAC;oDAAI,WAAU;8DACZ,SAAS,OAAO,SAAS;;;;;;;;;;;;sDAI9B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAA0B;;;;;;8DACzC,8OAAC;oDAAI,WAAU;8DACZ,SAAS,OAAO,SAAS;;;;;;;;;;;;sDAI9B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAA0B;;;;;;8DACzC,8OAAC;oDAAI,WAAU;8DACZ,SAAS,OAAO,SAAS;;;;;;;;;;;;sDAI9B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAA0B;;;;;;8DACzC,8OAAC;oDAAI,WAAU;8DACZ,SAAS,OAAO,UAAU;;;;;;;;;;;;;;;;;;8CAMjC,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;8BAKnB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,SAAS,OAAO,UAAU;;;;;;sCAG7B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;;kEAC/E,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;kEACrE,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDACX,SAAS,OAAO,SAAS;;;;;;sDAE5B,8OAAC;4CAAE,WAAU;sDACV,SAAS,OACN,8BACA;;;;;;;;;;;;8CAKR,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAyB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAChF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDACX,SAAS,OAAO,SAAS;;;;;;sDAE5B,8OAAC;4CAAE,WAAU;sDACV,SAAS,OACN,+BACA;;;;;;;;;;;;8CAKR,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDACX,SAAS,OAAO,SAAS;;;;;;sDAE5B,8OAAC;4CAAE,WAAU;sDACV,SAAS,OACN,8BACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpB", "debugId": null}}, {"offset": {"line": 1431, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/official-website/zebra-logistics/src/components/Contact.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Contact.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Contact.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8R,GAC3T,4DACA", "debugId": null}}, {"offset": {"line": 1445, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/official-website/zebra-logistics/src/components/Contact.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Contact.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Contact.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0Q,GACvS,wCACA", "debugId": null}}, {"offset": {"line": 1459, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1469, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/official-website/zebra-logistics/src/components/ScrollContainer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ScrollContainer.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ScrollContainer.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "debugId": null}}, {"offset": {"line": 1483, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/official-website/zebra-logistics/src/components/ScrollContainer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ScrollContainer.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ScrollContainer.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "debugId": null}}, {"offset": {"line": 1497, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1507, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/official-website/zebra-logistics/src/app/%5Blang%5D/page.tsx"], "sourcesContent": ["import { Locale, getDictionary } from '@/lib/i18n'\nimport Hero from '@/components/Hero'\nimport Services from '@/components/Services'\nimport TrackingSection from '@/components/TrackingSection'\nimport StatsSection from '@/components/StatsSection'\nimport About from '@/components/About'\nimport Contact from '@/components/Contact'\nimport ScrollContainer from '@/components/ScrollContainer'\n\nexport default async function HomePage({\n  params,\n}: {\n  params: Promise<{ lang: Locale }>\n}) {\n  const { lang } = await params\n  const dict = await getDictionary(lang)\n\n  const sectionIds = ['hero', 'services', 'tracking', 'stats', 'about', 'contact']\n\n  return (\n    <ScrollContainer sectionIds={sectionIds} lang={lang}>\n      <Hero dict={dict} lang={lang} />\n      <Services dict={dict} lang={lang} />\n      <TrackingSection dict={dict} lang={lang} />\n      <StatsSection dict={dict} lang={lang} />\n      <About dict={dict} lang={lang} />\n      <Contact dict={dict} lang={lang} />\n    </ScrollContainer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEe,eAAe,SAAS,EACrC,MAAM,EAGP;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IACvB,MAAM,OAAO,MAAM,CAAA,GAAA,kIAAA,CAAA,gBAAa,AAAD,EAAE;IAEjC,MAAM,aAAa;QAAC;QAAQ;QAAY;QAAY;QAAS;QAAS;KAAU;IAEhF,qBACE,8OAAC,qIAAA,CAAA,UAAe;QAAC,YAAY;QAAY,MAAM;;0BAC7C,8OAAC,0HAAA,CAAA,UAAI;gBAAC,MAAM;gBAAM,MAAM;;;;;;0BACxB,8OAAC,8HAAA,CAAA,UAAQ;gBAAC,MAAM;gBAAM,MAAM;;;;;;0BAC5B,8OAAC,qIAAA,CAAA,UAAe;gBAAC,MAAM;gBAAM,MAAM;;;;;;0BACnC,8OAAC,kIAAA,CAAA,UAAY;gBAAC,MAAM;gBAAM,MAAM;;;;;;0BAChC,8OAAC,2HAAA,CAAA,UAAK;gBAAC,MAAM;gBAAM,MAAM;;;;;;0BACzB,8OAAC,6HAAA,CAAA,UAAO;gBAAC,MAAM;gBAAM,MAAM;;;;;;;;;;;;AAGjC", "debugId": null}}]}