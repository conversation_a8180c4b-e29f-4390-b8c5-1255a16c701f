{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/official-website/zebra-logistics/src/components/Contact.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Locale } from '@/lib/types'\n\ninterface ContactProps {\n  dict: any\n  lang: Locale\n}\n\nexport default function Contact({ dict, lang }: ContactProps) {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    company: '',\n    message: ''\n  })\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault()\n    // TODO: Implement form submission logic\n    console.log('Form submitted:', formData)\n    alert(lang === 'zh' ? '感谢您的留言，我们会尽快回复！' : 'メッセージありがとうございます。すぐに返信いたします！')\n  }\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    })\n  }\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl font-bold text-gray-900 mb-4\">\n            {dict.contact.title}\n          </h2>\n          <p className=\"text-xl text-gray-600\">\n            {dict.contact.subtitle}\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16\">\n          {/* Contact Information */}\n          <div>\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-8\">\n              {lang === 'zh' ? '联系信息' : '連絡先情報'}\n            </h3>\n            \n            <div className=\"space-y-6\">\n              <div className=\"flex items-start\">\n                <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0\">\n                  <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                  </svg>\n                </div>\n                <div>\n                  <h4 className=\"text-lg font-semibold text-gray-900 mb-1\">\n                    {dict.contact.address}\n                  </h4>\n                  <p className=\"text-gray-600\">\n                    {dict.contact.address_text}\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start\">\n                <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0\">\n                  <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                  </svg>\n                </div>\n                <div>\n                  <h4 className=\"text-lg font-semibold text-gray-900 mb-1\">\n                    {dict.contact.phone}\n                  </h4>\n                  <p className=\"text-gray-600\">\n                    +86 591 8888 8888\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start\">\n                <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0\">\n                  <svg className=\"w-6 h-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                  </svg>\n                </div>\n                <div>\n                  <h4 className=\"text-lg font-semibold text-gray-900 mb-1\">\n                    {dict.contact.email}\n                  </h4>\n                  <p className=\"text-gray-600\">\n                    <EMAIL>\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start\">\n                <div className=\"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0\">\n                  <svg className=\"w-6 h-6 text-yellow-600\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n                  </svg>\n                </div>\n                <div>\n                  <h4 className=\"text-lg font-semibold text-gray-900 mb-1\">\n                    {dict.contact.wechat}\n                  </h4>\n                  <p className=\"text-gray-600\">\n                    zebra_logistics\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Business Hours */}\n            <div className=\"mt-8 bg-white rounded-lg p-6 shadow-md\">\n              <h4 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                {lang === 'zh' ? '营业时间' : '営業時間'}\n              </h4>\n              <div className=\"space-y-2 text-gray-600\">\n                <div className=\"flex justify-between\">\n                  <span>{lang === 'zh' ? '周一至周五' : '月曜日〜金曜日'}</span>\n                  <span>9:00 - 18:00</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span>{lang === 'zh' ? '周六' : '土曜日'}</span>\n                  <span>9:00 - 12:00</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span>{lang === 'zh' ? '周日' : '日曜日'}</span>\n                  <span>{lang === 'zh' ? '休息' : '休み'}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Contact Form */}\n          <div>\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-8\">\n              {lang === 'zh' ? '在线咨询' : 'オンライン相談'}\n            </h3>\n            \n            <form onSubmit={handleSubmit} className=\"bg-white rounded-lg p-8 shadow-md\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n                <div>\n                  <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    {dict.contact.form.name} <span className=\"text-red-500\">*</span>\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"name\"\n                    name=\"name\"\n                    required\n                    value={formData.name}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n                  />\n                </div>\n                \n                <div>\n                  <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    {dict.contact.form.email} <span className=\"text-red-500\">*</span>\n                  </label>\n                  <input\n                    type=\"email\"\n                    id=\"email\"\n                    name=\"email\"\n                    required\n                    value={formData.email}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n                <div>\n                  <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    {dict.contact.form.phone}\n                  </label>\n                  <input\n                    type=\"tel\"\n                    id=\"phone\"\n                    name=\"phone\"\n                    value={formData.phone}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n                  />\n                </div>\n                \n                <div>\n                  <label htmlFor=\"company\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    {dict.contact.form.company}\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"company\"\n                    name=\"company\"\n                    value={formData.company}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"mb-6\">\n                <label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  {dict.contact.form.message} <span className=\"text-red-500\">*</span>\n                </label>\n                <textarea\n                  id=\"message\"\n                  name=\"message\"\n                  rows={5}\n                  required\n                  value={formData.message}\n                  onChange={handleChange}\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors resize-none\"\n                ></textarea>\n              </div>\n\n              <button\n                type=\"submit\"\n                className=\"w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-200\"\n              >\n                {dict.contact.form.submit}\n              </button>\n            </form>\n          </div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUe,SAAS,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAgB;IAC1D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,SAAS;IACX;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,wCAAwC;QACxC,QAAQ,GAAG,CAAC,mBAAmB;QAC/B,MAAM,SAAS,OAAO,oBAAoB;IAC5C;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,KAAK,OAAO,CAAC,KAAK;;;;;;sCAErB,8OAAC;4BAAE,WAAU;sCACV,KAAK,OAAO,CAAC,QAAQ;;;;;;;;;;;;8BAI1B,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CACX,SAAS,OAAO,SAAS;;;;;;8CAG5B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;;0EAC/E,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;0EACrE,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;8DAGzE,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEACX,KAAK,OAAO,CAAC,OAAO;;;;;;sEAEvB,8OAAC;4DAAE,WAAU;sEACV,KAAK,OAAO,CAAC,YAAY;;;;;;;;;;;;;;;;;;sDAKhC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAyB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAChF,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEACX,KAAK,OAAO,CAAC,KAAK;;;;;;sEAErB,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAMjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAA0B,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACjF,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEACX,KAAK,OAAO,CAAC,KAAK;;;;;;sEAErB,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAMjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAA0B,MAAK;wDAAe,SAAQ;kEACnE,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEACX,KAAK,OAAO,CAAC,MAAM;;;;;;sEAEtB,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;8CAQnC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,SAAS,OAAO,SAAS;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAM,SAAS,OAAO,UAAU;;;;;;sEACjC,8OAAC;sEAAK;;;;;;;;;;;;8DAER,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAM,SAAS,OAAO,OAAO;;;;;;sEAC9B,8OAAC;sEAAK;;;;;;;;;;;;8DAER,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAM,SAAS,OAAO,OAAO;;;;;;sEAC9B,8OAAC;sEAAM,SAAS,OAAO,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOtC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CACX,SAAS,OAAO,SAAS;;;;;;8CAG5B,8OAAC;oCAAK,UAAU;oCAAc,WAAU;;sDACtC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAO,WAAU;;gEAC7B,KAAK,OAAO,CAAC,IAAI,CAAC,IAAI;gEAAC;8EAAC,8OAAC;oEAAK,WAAU;8EAAe;;;;;;;;;;;;sEAE1D,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,QAAQ;4DACR,OAAO,SAAS,IAAI;4DACpB,UAAU;4DACV,WAAU;;;;;;;;;;;;8DAId,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAQ,WAAU;;gEAC9B,KAAK,OAAO,CAAC,IAAI,CAAC,KAAK;gEAAC;8EAAC,8OAAC;oEAAK,WAAU;8EAAe;;;;;;;;;;;;sEAE3D,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,QAAQ;4DACR,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,WAAU;;;;;;;;;;;;;;;;;;sDAKhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAQ,WAAU;sEAC9B,KAAK,OAAO,CAAC,IAAI,CAAC,KAAK;;;;;;sEAE1B,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,WAAU;;;;;;;;;;;;8DAId,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAU,WAAU;sEAChC,KAAK,OAAO,CAAC,IAAI,CAAC,OAAO;;;;;;sEAE5B,8OAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,OAAO;4DACvB,UAAU;4DACV,WAAU;;;;;;;;;;;;;;;;;;sDAKhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAU,WAAU;;wDAChC,KAAK,OAAO,CAAC,IAAI,CAAC,OAAO;wDAAC;sEAAC,8OAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAE7D,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAM;oDACN,QAAQ;oDACR,OAAO,SAAS,OAAO;oDACvB,UAAU;oDACV,WAAU;;;;;;;;;;;;sDAId,8OAAC;4CACC,MAAK;4CACL,WAAU;sDAET,KAAK,OAAO,CAAC,IAAI,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzC", "debugId": null}}]}