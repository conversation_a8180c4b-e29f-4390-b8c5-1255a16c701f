{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__bb62ea2d._.js", "server/edge/chunks/edge-wrapper_7e21606c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|api|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|api|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "fOeIPrv6HZ0s/+Jmh2MJo4ubb3sSrXhFtzNrShNLAYU=", "__NEXT_PREVIEW_MODE_ID": "3b080781df18481cc2ce8d14c9fb53c4", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8f8d5b044ca7ac49689f2c29683e24775371a4a4621f51c0bc671dd135d259c6", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "413b6b28fb3a0d91068b3edb2ffdf203423677712291b6444e558c94db060673"}}}, "sortedMiddleware": ["/"], "functions": {}}