{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__bb62ea2d._.js", "server/edge/chunks/edge-wrapper_7e21606c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|api|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|api|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "fOeIPrv6HZ0s/+Jmh2MJo4ubb3sSrXhFtzNrShNLAYU=", "__NEXT_PREVIEW_MODE_ID": "f0b897fd59419d76205f549fee918efb", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "424779b5bc0c207fa47b07cd7d6b4f08f84bc37fc33ed59523463b91a020530e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "25d2761678c151bdd3ec9356061268425343297a79191efbe4ffc2f69481d854"}}}, "sortedMiddleware": ["/"], "functions": {}}