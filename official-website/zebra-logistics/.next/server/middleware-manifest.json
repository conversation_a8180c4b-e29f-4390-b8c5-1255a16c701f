{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__bb62ea2d._.js", "server/edge/chunks/edge-wrapper_7e21606c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|api|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|api|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "fOeIPrv6HZ0s/+Jmh2MJo4ubb3sSrXhFtzNrShNLAYU=", "__NEXT_PREVIEW_MODE_ID": "64bfa85e3b7f0487e55909d8620b53dd", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8898627f4ce5cd3221b36ba755a8bcc27e30c6429d3957d684b717a651d8b422", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8586a3895b1d6481eda97a140ad54ee17e103a716c65cd05d54921989907a8de"}}}, "sortedMiddleware": ["/"], "functions": {}}