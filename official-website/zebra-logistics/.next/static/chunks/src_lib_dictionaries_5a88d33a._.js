(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/dictionaries/zh.json (json, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_lib_dictionaries_zh_json_3589ee4e._.js",
  "static/chunks/src_lib_dictionaries_zh_json_46635c45._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/dictionaries/zh.json (json)");
    });
});
}}),
"[project]/src/lib/dictionaries/ja.json (json, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_lib_dictionaries_ja_json_c5426807._.js",
  "static/chunks/src_lib_dictionaries_ja_json_46635c45._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/dictionaries/ja.json (json)");
    });
});
}}),
}]);