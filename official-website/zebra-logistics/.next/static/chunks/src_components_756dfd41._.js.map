{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/official-website/zebra-logistics/src/components/TrackingSection.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Locale } from '@/lib/types'\n\ninterface TrackingSectionProps {\n  dict: any\n  lang: Locale\n}\n\nexport default function TrackingSection({ dict, lang }: TrackingSectionProps) {\n  const [trackingNumber, setTrackingNumber] = useState('')\n  const [isTracking, setIsTracking] = useState(false)\n  const [trackingResult, setTrackingResult] = useState<any>(null)\n\n  const handleTracking = async (e: React.FormEvent) => {\n    e.preventDefault()\n    if (!trackingNumber.trim()) return\n\n    setIsTracking(true)\n    \n    // 模拟API调用\n    setTimeout(() => {\n      setTrackingResult({\n        status: 'in_transit',\n        location: lang === 'zh' ? '福州国际机场' : '福州国際空港',\n        estimatedDelivery: '2024-01-15',\n        updates: [\n          {\n            time: '2024-01-10 14:30',\n            status: lang === 'zh' ? '已发货' : '発送済み',\n            location: lang === 'zh' ? '福州仓库' : '福州倉庫'\n          },\n          {\n            time: '2024-01-11 09:15',\n            status: lang === 'zh' ? '运输中' : '輸送中',\n            location: lang === 'zh' ? '福州国际机场' : '福州国際空港'\n          },\n          {\n            time: '2024-01-12 16:45',\n            status: lang === 'zh' ? '已到达日本' : '日本到着',\n            location: lang === 'zh' ? '东京成田机场' : '東京成田空港'\n          }\n        ]\n      })\n      setIsTracking(false)\n    }, 2000)\n  }\n\n  return (\n    <section className=\"py-20 gradient-bg text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-responsive-lg font-bold mb-4\">\n            {dict.tracking?.title || (lang === 'zh' ? '货物追踪' : '貨物追跡')}\n          </h2>\n          <p className=\"text-xl text-blue-100 max-w-3xl mx-auto\">\n            {dict.tracking?.subtitle || (lang === 'zh' ? '实时追踪您的货物状态，随时掌握物流信息' : 'リアルタイムで貨物の状況を追跡し、物流情報をいつでも把握')}\n          </p>\n        </div>\n\n        <div className=\"max-w-4xl mx-auto\">\n          <div className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20\">\n            <form onSubmit={handleTracking} className=\"mb-8\">\n              <div className=\"flex flex-col sm:flex-row gap-4\">\n                <div className=\"flex-1\">\n                  <input\n                    type=\"text\"\n                    value={trackingNumber}\n                    onChange={(e) => setTrackingNumber(e.target.value)}\n                    placeholder={lang === 'zh' ? '请输入运单号码' : '運送状番号を入力してください'}\n                    className=\"w-full px-6 py-4 text-gray-900 bg-white rounded-lg border-0 focus:ring-2 focus:ring-orange-500 text-lg\"\n                  />\n                </div>\n                <button\n                  type=\"submit\"\n                  disabled={isTracking || !trackingNumber.trim()}\n                  className=\"btn-primary px-8 py-4 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center min-w-[120px]\"\n                >\n                  {isTracking ? (\n                    <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-white\"></div>\n                  ) : (\n                    <>\n                      <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                      </svg>\n                      {lang === 'zh' ? '查询' : '検索'}\n                    </>\n                  )}\n                </button>\n              </div>\n            </form>\n\n            {trackingResult && (\n              <div className=\"bg-white rounded-xl p-6 text-gray-900 animate-fade-in-up\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <h3 className=\"text-xl font-semibold\">\n                    {lang === 'zh' ? '追踪结果' : '追跡結果'}\n                  </h3>\n                  <span className=\"px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium\">\n                    {lang === 'zh' ? '运输中' : '輸送中'}\n                  </span>\n                </div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n                  <div>\n                    <p className=\"text-sm text-gray-600 mb-1\">\n                      {lang === 'zh' ? '当前位置' : '現在位置'}\n                    </p>\n                    <p className=\"font-semibold\">{trackingResult.location}</p>\n                  </div>\n                  <div>\n                    <p className=\"text-sm text-gray-600 mb-1\">\n                      {lang === 'zh' ? '预计送达' : '配達予定'}\n                    </p>\n                    <p className=\"font-semibold\">{trackingResult.estimatedDelivery}</p>\n                  </div>\n                </div>\n\n                <div>\n                  <h4 className=\"font-semibold mb-4\">\n                    {lang === 'zh' ? '物流轨迹' : '物流軌跡'}\n                  </h4>\n                  <div className=\"space-y-4\">\n                    {trackingResult.updates.map((update: any, index: number) => (\n                      <div key={index} className=\"flex items-start space-x-4\">\n                        <div className=\"flex-shrink-0 w-3 h-3 bg-blue-500 rounded-full mt-2\"></div>\n                        <div className=\"flex-1\">\n                          <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n                            <p className=\"font-medium\">{update.status}</p>\n                            <p className=\"text-sm text-gray-500\">{update.time}</p>\n                          </div>\n                          <p className=\"text-sm text-gray-600\">{update.location}</p>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* 快速入口 */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mt-12\">\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 text-center hover:bg-white/20 transition-all duration-300 cursor-pointer\">\n              <div className=\"w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n                </svg>\n              </div>\n              <h3 className=\"font-semibold mb-2\">\n                {lang === 'zh' ? '在线报价' : 'オンライン見積もり'}\n              </h3>\n              <p className=\"text-sm text-blue-100\">\n                {lang === 'zh' ? '快速获取运费报价' : '運賃の見積もりを迅速に取得'}\n              </p>\n            </div>\n\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 text-center hover:bg-white/20 transition-all duration-300 cursor-pointer\">\n              <div className=\"w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                </svg>\n              </div>\n              <h3 className=\"font-semibold mb-2\">\n                {lang === 'zh' ? '网点查询' : '営業所検索'}\n              </h3>\n              <p className=\"text-sm text-blue-100\">\n                {lang === 'zh' ? '查找附近服务网点' : '近くのサービス拠点を検索'}\n              </p>\n            </div>\n\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 text-center hover:bg-white/20 transition-all duration-300 cursor-pointer\">\n              <div className=\"w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n              <h3 className=\"font-semibold mb-2\">\n                {lang === 'zh' ? '客服支持' : 'カスタマーサポート'}\n              </h3>\n              <p className=\"text-sm text-blue-100\">\n                {lang === 'zh' ? '24/7在线客服' : '24/7オンラインサポート'}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAUe,SAAS,gBAAgB,EAAE,IAAI,EAAE,IAAI,EAAwB;;IAC1E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAE1D,MAAM,iBAAiB,OAAO;QAC5B,EAAE,cAAc;QAChB,IAAI,CAAC,eAAe,IAAI,IAAI;QAE5B,cAAc;QAEd,UAAU;QACV,WAAW;YACT,kBAAkB;gBAChB,QAAQ;gBACR,UAAU,SAAS,OAAO,WAAW;gBACrC,mBAAmB;gBACnB,SAAS;oBACP;wBACE,MAAM;wBACN,QAAQ,SAAS,OAAO,QAAQ;wBAChC,UAAU,SAAS,OAAO,SAAS;oBACrC;oBACA;wBACE,MAAM;wBACN,QAAQ,SAAS,OAAO,QAAQ;wBAChC,UAAU,SAAS,OAAO,WAAW;oBACvC;oBACA;wBACE,MAAM;wBACN,QAAQ,SAAS,OAAO,UAAU;wBAClC,UAAU,SAAS,OAAO,WAAW;oBACvC;iBACD;YACH;YACA,cAAc;QAChB,GAAG;IACL;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,KAAK,QAAQ,EAAE,SAAS,CAAC,SAAS,OAAO,SAAS,MAAM;;;;;;sCAE3D,6LAAC;4BAAE,WAAU;sCACV,KAAK,QAAQ,EAAE,YAAY,CAAC,SAAS,OAAO,wBAAwB,8BAA8B;;;;;;;;;;;;8BAIvG,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,UAAU;oCAAgB,WAAU;8CACxC,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;oDACjD,aAAa,SAAS,OAAO,YAAY;oDACzC,WAAU;;;;;;;;;;;0DAGd,6LAAC;gDACC,MAAK;gDACL,UAAU,cAAc,CAAC,eAAe,IAAI;gDAC5C,WAAU;0DAET,2BACC,6LAAC;oDAAI,WAAU;;;;;yEAEf;;sEACE,6LAAC;4DAAI,WAAU;4DAAe,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACtE,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;wDAEtE,SAAS,OAAO,OAAO;;;;;;;;;;;;;;;;;;;gCAOjC,gCACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,SAAS,OAAO,SAAS;;;;;;8DAE5B,6LAAC;oDAAK,WAAU;8DACb,SAAS,OAAO,QAAQ;;;;;;;;;;;;sDAI7B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEACV,SAAS,OAAO,SAAS;;;;;;sEAE5B,6LAAC;4DAAE,WAAU;sEAAiB,eAAe,QAAQ;;;;;;;;;;;;8DAEvD,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEACV,SAAS,OAAO,SAAS;;;;;;sEAE5B,6LAAC;4DAAE,WAAU;sEAAiB,eAAe,iBAAiB;;;;;;;;;;;;;;;;;;sDAIlE,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DACX,SAAS,OAAO,SAAS;;;;;;8DAE5B,6LAAC;oDAAI,WAAU;8DACZ,eAAe,OAAO,CAAC,GAAG,CAAC,CAAC,QAAa,sBACxC,6LAAC;4DAAgB,WAAU;;8EACzB,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAE,WAAU;8FAAe,OAAO,MAAM;;;;;;8FACzC,6LAAC;oFAAE,WAAU;8FAAyB,OAAO,IAAI;;;;;;;;;;;;sFAEnD,6LAAC;4EAAE,WAAU;sFAAyB,OAAO,QAAQ;;;;;;;;;;;;;2DAP/C;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAkBtB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5E,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAG,WAAU;sDACX,SAAS,OAAO,SAAS;;;;;;sDAE5B,6LAAC;4CAAE,WAAU;sDACV,SAAS,OAAO,aAAa;;;;;;;;;;;;8CAIlC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;;kEAC5E,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;kEACrE,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAG,WAAU;sDACX,SAAS,OAAO,SAAS;;;;;;sDAE5B,6LAAC;4CAAE,WAAU;sDACV,SAAS,OAAO,aAAa;;;;;;;;;;;;8CAIlC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5E,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAG,WAAU;sDACX,SAAS,OAAO,SAAS;;;;;;sDAE5B,6LAAC;4CAAE,WAAU;sDACV,SAAS,OAAO,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9C;GArLwB;KAAA", "debugId": null}}, {"offset": {"line": 549, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/official-website/zebra-logistics/src/components/StatsSection.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Locale } from '@/lib/types'\nimport Image from 'next/image'\n\ninterface StatsSectionProps {\n  dict: any\n  lang: Locale\n}\n\ninterface CounterProps {\n  end: number\n  duration: number\n  suffix?: string\n}\n\nfunction Counter({ end, duration, suffix = '' }: CounterProps) {\n  const [count, setCount] = useState(0)\n\n  useEffect(() => {\n    let startTime: number\n    let animationFrame: number\n\n    const animate = (currentTime: number) => {\n      if (!startTime) startTime = currentTime\n      const progress = Math.min((currentTime - startTime) / duration, 1)\n      \n      setCount(Math.floor(progress * end))\n      \n      if (progress < 1) {\n        animationFrame = requestAnimationFrame(animate)\n      }\n    }\n\n    animationFrame = requestAnimationFrame(animate)\n    \n    return () => {\n      if (animationFrame) {\n        cancelAnimationFrame(animationFrame)\n      }\n    }\n  }, [end, duration])\n\n  return <span>{count}{suffix}</span>\n}\n\nexport default function StatsSection({ dict, lang }: StatsSectionProps) {\n  const stats = [\n    {\n      number: 2020,\n      suffix: '',\n      label: lang === 'zh' ? '成立年份' : '設立年',\n      description: lang === 'zh' ? '专注跨境物流服务' : '越境物流サービスに特化',\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n        </svg>\n      ),\n      color: 'from-blue-500 to-blue-600'\n    },\n    {\n      number: 1000,\n      suffix: '+',\n      label: lang === 'zh' ? '服务客户' : 'サービス顧客',\n      description: lang === 'zh' ? '遍布中日两国的客户群体' : '中日両国に広がる顧客層',\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n        </svg>\n      ),\n      color: 'from-green-500 to-green-600'\n    },\n    {\n      number: 50,\n      suffix: '+',\n      label: lang === 'zh' ? '专业团队' : '専門チーム',\n      description: lang === 'zh' ? '经验丰富的物流专家' : '経験豊富な物流専門家',\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\" />\n        </svg>\n      ),\n      color: 'from-purple-500 to-purple-600'\n    },\n    {\n      number: 99,\n      suffix: '%',\n      label: lang === 'zh' ? '客户满意度' : '顧客満足度',\n      description: lang === 'zh' ? '持续提供优质服务' : '継続的に優良サービスを提供',\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n        </svg>\n      ),\n      color: 'from-orange-500 to-red-500'\n    }\n  ]\n\n  return (\n    <section className=\"py-20 bg-gray-50 relative overflow-hidden\">\n      {/* 背景图片 */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <Image\n          src=\"/images/logistics-control-center.webp\"\n          alt=\"Logistics Control Center\"\n          fill\n          className=\"object-cover\"\n        />\n      </div>\n      \n      <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-responsive-lg font-bold text-gray-900 mb-4\">\n            {dict.stats?.title || (lang === 'zh' ? '数据说话' : 'データで語る')}\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            {dict.stats?.subtitle || (lang === 'zh' ? '用数字见证我们的专业实力和服务品质' : '数字で私たちの専門力とサービス品質を証明')}\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {stats.map((stat, index) => (\n            <div\n              key={index}\n              className=\"card p-8 text-center group hover:scale-105 transition-all duration-300\"\n            >\n              <div className={`w-16 h-16 bg-gradient-to-r ${stat.color} rounded-2xl flex items-center justify-center mx-auto mb-6 text-white group-hover:scale-110 transition-transform duration-300`}>\n                {stat.icon}\n              </div>\n              \n              <div className=\"mb-4\">\n                <div className=\"text-4xl font-bold text-gray-900 mb-2\">\n                  <Counter end={stat.number} duration={2000} suffix={stat.suffix} />\n                </div>\n                <h3 className=\"text-lg font-semibold text-gray-800 mb-2\">\n                  {stat.label}\n                </h3>\n                <p className=\"text-sm text-gray-600\">\n                  {stat.description}\n                </p>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* 成就展示 */}\n        <div className=\"mt-20\">\n          <div className=\"text-center mb-12\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              {lang === 'zh' ? '我们的成就' : '私たちの実績'}\n            </h3>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"bg-white rounded-xl p-6 shadow-lg border border-gray-100\">\n              <div className=\"flex items-center mb-4\">\n                <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4\">\n                  <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                </div>\n                <h4 className=\"text-lg font-semibold text-gray-900\">\n                  {lang === 'zh' ? '资质认证' : '資格認証'}\n                </h4>\n              </div>\n              <p className=\"text-gray-600\">\n                {lang === 'zh' ? '获得国际货运代理资质，海关AEO认证企业' : '国際貨物輸送代理資格、税関AEO認証企業を取得'}\n              </p>\n            </div>\n\n            <div className=\"bg-white rounded-xl p-6 shadow-lg border border-gray-100\">\n              <div className=\"flex items-center mb-4\">\n                <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4\">\n                  <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                  </svg>\n                </div>\n                <h4 className=\"text-lg font-semibold text-gray-900\">\n                  {lang === 'zh' ? '服务网络' : 'サービスネットワーク'}\n                </h4>\n              </div>\n              <p className=\"text-gray-600\">\n                {lang === 'zh' ? '覆盖福州-日本主要城市的完整物流网络' : '福州-日本主要都市をカバーする完全な物流ネットワーク'}\n              </p>\n            </div>\n\n            <div className=\"bg-white rounded-xl p-6 shadow-lg border border-gray-100\">\n              <div className=\"flex items-center mb-4\">\n                <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4\">\n                  <svg className=\"w-6 h-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                </div>\n                <h4 className=\"text-lg font-semibold text-gray-900\">\n                  {lang === 'zh' ? '时效保障' : '時間保証'}\n                </h4>\n              </div>\n              <p className=\"text-gray-600\">\n                {lang === 'zh' ? '平均3-5个工作日送达，准时率达99%以上' : '平均3-5営業日で配達、定時率99%以上'}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;;;AAJA;;;AAiBA,SAAS,QAAQ,EAAE,GAAG,EAAE,QAAQ,EAAE,SAAS,EAAE,EAAgB;;IAC3D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI;YACJ,IAAI;YAEJ,MAAM;6CAAU,CAAC;oBACf,IAAI,CAAC,WAAW,YAAY;oBAC5B,MAAM,WAAW,KAAK,GAAG,CAAC,CAAC,cAAc,SAAS,IAAI,UAAU;oBAEhE,SAAS,KAAK,KAAK,CAAC,WAAW;oBAE/B,IAAI,WAAW,GAAG;wBAChB,iBAAiB,sBAAsB;oBACzC;gBACF;;YAEA,iBAAiB,sBAAsB;YAEvC;qCAAO;oBACL,IAAI,gBAAgB;wBAClB,qBAAqB;oBACvB;gBACF;;QACF;4BAAG;QAAC;QAAK;KAAS;IAElB,qBAAO,6LAAC;;YAAM;YAAO;;;;;;;AACvB;GA5BS;KAAA;AA8BM,SAAS,aAAa,EAAE,IAAI,EAAE,IAAI,EAAqB;IACpE,MAAM,QAAQ;QACZ;YACE,QAAQ;YACR,QAAQ;YACR,OAAO,SAAS,OAAO,SAAS;YAChC,aAAa,SAAS,OAAO,aAAa;YAC1C,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;QACT;QACA;YACE,QAAQ;YACR,QAAQ;YACR,OAAO,SAAS,OAAO,SAAS;YAChC,aAAa,SAAS,OAAO,gBAAgB;YAC7C,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;QACT;QACA;YACE,QAAQ;YACR,QAAQ;YACR,OAAO,SAAS,OAAO,SAAS;YAChC,aAAa,SAAS,OAAO,cAAc;YAC3C,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;QACT;QACA;YACE,QAAQ;YACR,QAAQ;YACR,OAAO,SAAS,OAAO,UAAU;YACjC,aAAa,SAAS,OAAO,aAAa;YAC1C,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oBACJ,KAAI;oBACJ,KAAI;oBACJ,IAAI;oBACJ,WAAU;;;;;;;;;;;0BAId,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX,KAAK,KAAK,EAAE,SAAS,CAAC,SAAS,OAAO,SAAS,QAAQ;;;;;;0CAE1D,6LAAC;gCAAE,WAAU;0CACV,KAAK,KAAK,EAAE,YAAY,CAAC,SAAS,OAAO,sBAAsB,sBAAsB;;;;;;;;;;;;kCAI1F,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;wCAAI,WAAW,CAAC,2BAA2B,EAAE,KAAK,KAAK,CAAC,6HAA6H,CAAC;kDACpL,KAAK,IAAI;;;;;;kDAGZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAQ,KAAK,KAAK,MAAM;oDAAE,UAAU;oDAAM,QAAQ,KAAK,MAAM;;;;;;;;;;;0DAEhE,6LAAC;gDAAG,WAAU;0DACX,KAAK,KAAK;;;;;;0DAEb,6LAAC;gDAAE,WAAU;0DACV,KAAK,WAAW;;;;;;;;;;;;;+BAfhB;;;;;;;;;;kCAuBX,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CACX,SAAS,OAAO,UAAU;;;;;;;;;;;0CAI/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAwB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC/E,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,6LAAC;wDAAG,WAAU;kEACX,SAAS,OAAO,SAAS;;;;;;;;;;;;0DAG9B,6LAAC;gDAAE,WAAU;0DACV,SAAS,OAAO,yBAAyB;;;;;;;;;;;;kDAI9C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAyB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAChF,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,6LAAC;wDAAG,WAAU;kEACX,SAAS,OAAO,SAAS;;;;;;;;;;;;0DAG9B,6LAAC;gDAAE,WAAU;0DACV,SAAS,OAAO,uBAAuB;;;;;;;;;;;;kDAI5C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAA0B,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACjF,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,6LAAC;wDAAG,WAAU;kEACX,SAAS,OAAO,SAAS;;;;;;;;;;;;0DAG9B,6LAAC;gDAAE,WAAU;0DACV,SAAS,OAAO,0BAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3D;MAhKwB", "debugId": null}}, {"offset": {"line": 1069, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/official-website/zebra-logistics/src/components/Contact.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Locale } from '@/lib/types'\n\ninterface ContactProps {\n  dict: any\n  lang: Locale\n}\n\nexport default function Contact({ dict, lang }: ContactProps) {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    company: '',\n    message: ''\n  })\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault()\n    // TODO: Implement form submission logic\n    console.log('Form submitted:', formData)\n    alert(lang === 'zh' ? '感谢您的留言，我们会尽快回复！' : 'メッセージありがとうございます。すぐに返信いたします！')\n  }\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    })\n  }\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl font-bold text-gray-900 mb-4\">\n            {dict.contact.title}\n          </h2>\n          <p className=\"text-xl text-gray-600\">\n            {dict.contact.subtitle}\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16\">\n          {/* Contact Information */}\n          <div>\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-8\">\n              {lang === 'zh' ? '联系信息' : '連絡先情報'}\n            </h3>\n            \n            <div className=\"space-y-6\">\n              <div className=\"flex items-start\">\n                <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0\">\n                  <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                  </svg>\n                </div>\n                <div>\n                  <h4 className=\"text-lg font-semibold text-gray-900 mb-1\">\n                    {dict.contact.address}\n                  </h4>\n                  <p className=\"text-gray-600\">\n                    {dict.contact.address_text}\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start\">\n                <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0\">\n                  <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                  </svg>\n                </div>\n                <div>\n                  <h4 className=\"text-lg font-semibold text-gray-900 mb-1\">\n                    {dict.contact.phone}\n                  </h4>\n                  <p className=\"text-gray-600\">\n                    +86 591 8888 8888\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start\">\n                <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0\">\n                  <svg className=\"w-6 h-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                  </svg>\n                </div>\n                <div>\n                  <h4 className=\"text-lg font-semibold text-gray-900 mb-1\">\n                    {dict.contact.email}\n                  </h4>\n                  <p className=\"text-gray-600\">\n                    <EMAIL>\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start\">\n                <div className=\"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0\">\n                  <svg className=\"w-6 h-6 text-yellow-600\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n                  </svg>\n                </div>\n                <div>\n                  <h4 className=\"text-lg font-semibold text-gray-900 mb-1\">\n                    {dict.contact.wechat}\n                  </h4>\n                  <p className=\"text-gray-600\">\n                    zebra_logistics\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Business Hours */}\n            <div className=\"mt-8 bg-white rounded-lg p-6 shadow-md\">\n              <h4 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                {lang === 'zh' ? '营业时间' : '営業時間'}\n              </h4>\n              <div className=\"space-y-2 text-gray-600\">\n                <div className=\"flex justify-between\">\n                  <span>{lang === 'zh' ? '周一至周五' : '月曜日〜金曜日'}</span>\n                  <span>9:00 - 18:00</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span>{lang === 'zh' ? '周六' : '土曜日'}</span>\n                  <span>9:00 - 12:00</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span>{lang === 'zh' ? '周日' : '日曜日'}</span>\n                  <span>{lang === 'zh' ? '休息' : '休み'}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Contact Form */}\n          <div>\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-8\">\n              {lang === 'zh' ? '在线咨询' : 'オンライン相談'}\n            </h3>\n            \n            <form onSubmit={handleSubmit} className=\"bg-white rounded-lg p-8 shadow-md\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n                <div>\n                  <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    {dict.contact.form.name} <span className=\"text-red-500\">*</span>\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"name\"\n                    name=\"name\"\n                    required\n                    value={formData.name}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n                  />\n                </div>\n                \n                <div>\n                  <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    {dict.contact.form.email} <span className=\"text-red-500\">*</span>\n                  </label>\n                  <input\n                    type=\"email\"\n                    id=\"email\"\n                    name=\"email\"\n                    required\n                    value={formData.email}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n                <div>\n                  <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    {dict.contact.form.phone}\n                  </label>\n                  <input\n                    type=\"tel\"\n                    id=\"phone\"\n                    name=\"phone\"\n                    value={formData.phone}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n                  />\n                </div>\n                \n                <div>\n                  <label htmlFor=\"company\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    {dict.contact.form.company}\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"company\"\n                    name=\"company\"\n                    value={formData.company}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"mb-6\">\n                <label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  {dict.contact.form.message} <span className=\"text-red-500\">*</span>\n                </label>\n                <textarea\n                  id=\"message\"\n                  name=\"message\"\n                  rows={5}\n                  required\n                  value={formData.message}\n                  onChange={handleChange}\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors resize-none\"\n                ></textarea>\n              </div>\n\n              <button\n                type=\"submit\"\n                className=\"w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-200\"\n              >\n                {dict.contact.form.submit}\n              </button>\n            </form>\n          </div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAUe,SAAS,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAgB;;IAC1D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,SAAS;IACX;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,wCAAwC;QACxC,QAAQ,GAAG,CAAC,mBAAmB;QAC/B,MAAM,SAAS,OAAO,oBAAoB;IAC5C;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,KAAK,OAAO,CAAC,KAAK;;;;;;sCAErB,6LAAC;4BAAE,WAAU;sCACV,KAAK,OAAO,CAAC,QAAQ;;;;;;;;;;;;8BAI1B,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CACX,SAAS,OAAO,SAAS;;;;;;8CAG5B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;;0EAC/E,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;0EACrE,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;8DAGzE,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEACX,KAAK,OAAO,CAAC,OAAO;;;;;;sEAEvB,6LAAC;4DAAE,WAAU;sEACV,KAAK,OAAO,CAAC,YAAY;;;;;;;;;;;;;;;;;;sDAKhC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAyB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAChF,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEACX,KAAK,OAAO,CAAC,KAAK;;;;;;sEAErB,6LAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAMjC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAA0B,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACjF,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEACX,KAAK,OAAO,CAAC,KAAK;;;;;;sEAErB,6LAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAMjC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAA0B,MAAK;wDAAe,SAAQ;kEACnE,cAAA,6LAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEACX,KAAK,OAAO,CAAC,MAAM;;;;;;sEAEtB,6LAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;8CAQnC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDACX,SAAS,OAAO,SAAS;;;;;;sDAE5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAM,SAAS,OAAO,UAAU;;;;;;sEACjC,6LAAC;sEAAK;;;;;;;;;;;;8DAER,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAM,SAAS,OAAO,OAAO;;;;;;sEAC9B,6LAAC;sEAAK;;;;;;;;;;;;8DAER,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAM,SAAS,OAAO,OAAO;;;;;;sEAC9B,6LAAC;sEAAM,SAAS,OAAO,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOtC,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CACX,SAAS,OAAO,SAAS;;;;;;8CAG5B,6LAAC;oCAAK,UAAU;oCAAc,WAAU;;sDACtC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAO,WAAU;;gEAC7B,KAAK,OAAO,CAAC,IAAI,CAAC,IAAI;gEAAC;8EAAC,6LAAC;oEAAK,WAAU;8EAAe;;;;;;;;;;;;sEAE1D,6LAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,QAAQ;4DACR,OAAO,SAAS,IAAI;4DACpB,UAAU;4DACV,WAAU;;;;;;;;;;;;8DAId,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAQ,WAAU;;gEAC9B,KAAK,OAAO,CAAC,IAAI,CAAC,KAAK;gEAAC;8EAAC,6LAAC;oEAAK,WAAU;8EAAe;;;;;;;;;;;;sEAE3D,6LAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,QAAQ;4DACR,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,WAAU;;;;;;;;;;;;;;;;;;sDAKhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAQ,WAAU;sEAC9B,KAAK,OAAO,CAAC,IAAI,CAAC,KAAK;;;;;;sEAE1B,6LAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,WAAU;;;;;;;;;;;;8DAId,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAU,WAAU;sEAChC,KAAK,OAAO,CAAC,IAAI,CAAC,OAAO;;;;;;sEAE5B,6LAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,OAAO;4DACvB,UAAU;4DACV,WAAU;;;;;;;;;;;;;;;;;;sDAKhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,SAAQ;oDAAU,WAAU;;wDAChC,KAAK,OAAO,CAAC,IAAI,CAAC,OAAO;wDAAC;sEAAC,6LAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAE7D,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAM;oDACN,QAAQ;oDACR,OAAO,SAAS,OAAO;oDACvB,UAAU;oDACV,WAAU;;;;;;;;;;;;sDAId,6LAAC;4CACC,MAAK;4CACL,WAAU;sDAET,KAAK,OAAO,CAAC,IAAI,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzC;GAnOwB;KAAA", "debugId": null}}]}