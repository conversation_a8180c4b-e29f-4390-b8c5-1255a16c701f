{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/official-website/zebra-logistics/src/components/TrackingSection.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Locale } from '@/lib/types'\n\ninterface TrackingSectionProps {\n  dict: any\n  lang: Locale\n}\n\nexport default function TrackingSection({ dict, lang }: TrackingSectionProps) {\n  const [trackingNumber, setTrackingNumber] = useState('')\n  const [isTracking, setIsTracking] = useState(false)\n  const [trackingResult, setTrackingResult] = useState<any>(null)\n\n  const handleTracking = async (e: React.FormEvent) => {\n    e.preventDefault()\n    if (!trackingNumber.trim()) return\n\n    setIsTracking(true)\n    \n    // 模拟API调用\n    setTimeout(() => {\n      setTrackingResult({\n        status: 'in_transit',\n        location: lang === 'zh' ? '福州国际机场' : '福州国際空港',\n        estimatedDelivery: '2024-01-15',\n        updates: [\n          {\n            time: '2024-01-10 14:30',\n            status: lang === 'zh' ? '已发货' : '発送済み',\n            location: lang === 'zh' ? '福州仓库' : '福州倉庫'\n          },\n          {\n            time: '2024-01-11 09:15',\n            status: lang === 'zh' ? '运输中' : '輸送中',\n            location: lang === 'zh' ? '福州国际机场' : '福州国際空港'\n          },\n          {\n            time: '2024-01-12 16:45',\n            status: lang === 'zh' ? '已到达日本' : '日本到着',\n            location: lang === 'zh' ? '东京成田机场' : '東京成田空港'\n          }\n        ]\n      })\n      setIsTracking(false)\n    }, 2000)\n  }\n\n  return (\n    <section className=\"py-20 gradient-bg text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-responsive-lg font-bold mb-4\">\n            {dict.tracking?.title || (lang === 'zh' ? '货物追踪' : '貨物追跡')}\n          </h2>\n          <p className=\"text-xl text-blue-100 max-w-3xl mx-auto\">\n            {dict.tracking?.subtitle || (lang === 'zh' ? '实时追踪您的货物状态，随时掌握物流信息' : 'リアルタイムで貨物の状況を追跡し、物流情報をいつでも把握')}\n          </p>\n        </div>\n\n        <div className=\"max-w-4xl mx-auto\">\n          <div className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20\">\n            <form onSubmit={handleTracking} className=\"mb-8\">\n              <div className=\"flex flex-col sm:flex-row gap-4\">\n                <div className=\"flex-1\">\n                  <input\n                    type=\"text\"\n                    value={trackingNumber}\n                    onChange={(e) => setTrackingNumber(e.target.value)}\n                    placeholder={lang === 'zh' ? '请输入运单号码' : '運送状番号を入力してください'}\n                    className=\"w-full px-6 py-4 text-gray-900 bg-white rounded-lg border-0 focus:ring-2 focus:ring-orange-500 text-lg\"\n                  />\n                </div>\n                <button\n                  type=\"submit\"\n                  disabled={isTracking || !trackingNumber.trim()}\n                  className=\"btn-primary px-8 py-4 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center min-w-[120px]\"\n                >\n                  {isTracking ? (\n                    <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-white\"></div>\n                  ) : (\n                    <>\n                      <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                      </svg>\n                      {lang === 'zh' ? '查询' : '検索'}\n                    </>\n                  )}\n                </button>\n              </div>\n            </form>\n\n            {trackingResult && (\n              <div className=\"bg-white rounded-xl p-6 text-gray-900 animate-fade-in-up\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <h3 className=\"text-xl font-semibold\">\n                    {lang === 'zh' ? '追踪结果' : '追跡結果'}\n                  </h3>\n                  <span className=\"px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium\">\n                    {lang === 'zh' ? '运输中' : '輸送中'}\n                  </span>\n                </div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n                  <div>\n                    <p className=\"text-sm text-gray-600 mb-1\">\n                      {lang === 'zh' ? '当前位置' : '現在位置'}\n                    </p>\n                    <p className=\"font-semibold\">{trackingResult.location}</p>\n                  </div>\n                  <div>\n                    <p className=\"text-sm text-gray-600 mb-1\">\n                      {lang === 'zh' ? '预计送达' : '配達予定'}\n                    </p>\n                    <p className=\"font-semibold\">{trackingResult.estimatedDelivery}</p>\n                  </div>\n                </div>\n\n                <div>\n                  <h4 className=\"font-semibold mb-4\">\n                    {lang === 'zh' ? '物流轨迹' : '物流軌跡'}\n                  </h4>\n                  <div className=\"space-y-4\">\n                    {trackingResult.updates.map((update: any, index: number) => (\n                      <div key={index} className=\"flex items-start space-x-4\">\n                        <div className=\"flex-shrink-0 w-3 h-3 bg-blue-500 rounded-full mt-2\"></div>\n                        <div className=\"flex-1\">\n                          <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n                            <p className=\"font-medium\">{update.status}</p>\n                            <p className=\"text-sm text-gray-500\">{update.time}</p>\n                          </div>\n                          <p className=\"text-sm text-gray-600\">{update.location}</p>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* 快速入口 */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mt-12\">\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 text-center hover:bg-white/20 transition-all duration-300 cursor-pointer\">\n              <div className=\"w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n                </svg>\n              </div>\n              <h3 className=\"font-semibold mb-2\">\n                {lang === 'zh' ? '在线报价' : 'オンライン見積もり'}\n              </h3>\n              <p className=\"text-sm text-blue-100\">\n                {lang === 'zh' ? '快速获取运费报价' : '運賃の見積もりを迅速に取得'}\n              </p>\n            </div>\n\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 text-center hover:bg-white/20 transition-all duration-300 cursor-pointer\">\n              <div className=\"w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                </svg>\n              </div>\n              <h3 className=\"font-semibold mb-2\">\n                {lang === 'zh' ? '网点查询' : '営業所検索'}\n              </h3>\n              <p className=\"text-sm text-blue-100\">\n                {lang === 'zh' ? '查找附近服务网点' : '近くのサービス拠点を検索'}\n              </p>\n            </div>\n\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 text-center hover:bg-white/20 transition-all duration-300 cursor-pointer\">\n              <div className=\"w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n              <h3 className=\"font-semibold mb-2\">\n                {lang === 'zh' ? '客服支持' : 'カスタマーサポート'}\n              </h3>\n              <p className=\"text-sm text-blue-100\">\n                {lang === 'zh' ? '24/7在线客服' : '24/7オンラインサポート'}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAUe,SAAS,gBAAgB,EAAE,IAAI,EAAE,IAAI,EAAwB;;IAC1E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAE1D,MAAM,iBAAiB,OAAO;QAC5B,EAAE,cAAc;QAChB,IAAI,CAAC,eAAe,IAAI,IAAI;QAE5B,cAAc;QAEd,UAAU;QACV,WAAW;YACT,kBAAkB;gBAChB,QAAQ;gBACR,UAAU,SAAS,OAAO,WAAW;gBACrC,mBAAmB;gBACnB,SAAS;oBACP;wBACE,MAAM;wBACN,QAAQ,SAAS,OAAO,QAAQ;wBAChC,UAAU,SAAS,OAAO,SAAS;oBACrC;oBACA;wBACE,MAAM;wBACN,QAAQ,SAAS,OAAO,QAAQ;wBAChC,UAAU,SAAS,OAAO,WAAW;oBACvC;oBACA;wBACE,MAAM;wBACN,QAAQ,SAAS,OAAO,UAAU;wBAClC,UAAU,SAAS,OAAO,WAAW;oBACvC;iBACD;YACH;YACA,cAAc;QAChB,GAAG;IACL;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,KAAK,QAAQ,EAAE,SAAS,CAAC,SAAS,OAAO,SAAS,MAAM;;;;;;sCAE3D,6LAAC;4BAAE,WAAU;sCACV,KAAK,QAAQ,EAAE,YAAY,CAAC,SAAS,OAAO,wBAAwB,8BAA8B;;;;;;;;;;;;8BAIvG,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,UAAU;oCAAgB,WAAU;8CACxC,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;oDACjD,aAAa,SAAS,OAAO,YAAY;oDACzC,WAAU;;;;;;;;;;;0DAGd,6LAAC;gDACC,MAAK;gDACL,UAAU,cAAc,CAAC,eAAe,IAAI;gDAC5C,WAAU;0DAET,2BACC,6LAAC;oDAAI,WAAU;;;;;yEAEf;;sEACE,6LAAC;4DAAI,WAAU;4DAAe,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACtE,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;wDAEtE,SAAS,OAAO,OAAO;;;;;;;;;;;;;;;;;;;gCAOjC,gCACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,SAAS,OAAO,SAAS;;;;;;8DAE5B,6LAAC;oDAAK,WAAU;8DACb,SAAS,OAAO,QAAQ;;;;;;;;;;;;sDAI7B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEACV,SAAS,OAAO,SAAS;;;;;;sEAE5B,6LAAC;4DAAE,WAAU;sEAAiB,eAAe,QAAQ;;;;;;;;;;;;8DAEvD,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEACV,SAAS,OAAO,SAAS;;;;;;sEAE5B,6LAAC;4DAAE,WAAU;sEAAiB,eAAe,iBAAiB;;;;;;;;;;;;;;;;;;sDAIlE,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DACX,SAAS,OAAO,SAAS;;;;;;8DAE5B,6LAAC;oDAAI,WAAU;8DACZ,eAAe,OAAO,CAAC,GAAG,CAAC,CAAC,QAAa,sBACxC,6LAAC;4DAAgB,WAAU;;8EACzB,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAE,WAAU;8FAAe,OAAO,MAAM;;;;;;8FACzC,6LAAC;oFAAE,WAAU;8FAAyB,OAAO,IAAI;;;;;;;;;;;;sFAEnD,6LAAC;4EAAE,WAAU;sFAAyB,OAAO,QAAQ;;;;;;;;;;;;;2DAP/C;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAkBtB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5E,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAG,WAAU;sDACX,SAAS,OAAO,SAAS;;;;;;sDAE5B,6LAAC;4CAAE,WAAU;sDACV,SAAS,OAAO,aAAa;;;;;;;;;;;;8CAIlC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;;kEAC5E,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;kEACrE,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAG,WAAU;sDACX,SAAS,OAAO,SAAS;;;;;;sDAE5B,6LAAC;4CAAE,WAAU;sDACV,SAAS,OAAO,aAAa;;;;;;;;;;;;8CAIlC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5E,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAG,WAAU;sDACX,SAAS,OAAO,SAAS;;;;;;sDAE5B,6LAAC;4CAAE,WAAU;sDACV,SAAS,OAAO,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9C;GArLwB;KAAA", "debugId": null}}, {"offset": {"line": 549, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/official-website/zebra-logistics/src/components/StatsSection.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Locale } from '@/lib/types'\nimport Image from 'next/image'\n\ninterface StatsSectionProps {\n  dict: any\n  lang: Locale\n}\n\ninterface CounterProps {\n  end: number\n  duration: number\n  suffix?: string\n}\n\nfunction Counter({ end, duration, suffix = '' }: CounterProps) {\n  const [count, setCount] = useState(0)\n\n  useEffect(() => {\n    let startTime: number\n    let animationFrame: number\n\n    const animate = (currentTime: number) => {\n      if (!startTime) startTime = currentTime\n      const progress = Math.min((currentTime - startTime) / duration, 1)\n      \n      setCount(Math.floor(progress * end))\n      \n      if (progress < 1) {\n        animationFrame = requestAnimationFrame(animate)\n      }\n    }\n\n    animationFrame = requestAnimationFrame(animate)\n    \n    return () => {\n      if (animationFrame) {\n        cancelAnimationFrame(animationFrame)\n      }\n    }\n  }, [end, duration])\n\n  return <span>{count}{suffix}</span>\n}\n\nexport default function StatsSection({ dict, lang }: StatsSectionProps) {\n  const stats = [\n    {\n      number: 2020,\n      suffix: '',\n      label: lang === 'zh' ? '成立年份' : '設立年',\n      description: lang === 'zh' ? '专注跨境物流服务' : '越境物流サービスに特化',\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n        </svg>\n      ),\n      color: 'from-blue-500 to-blue-600'\n    },\n    {\n      number: 1000,\n      suffix: '+',\n      label: lang === 'zh' ? '服务客户' : 'サービス顧客',\n      description: lang === 'zh' ? '遍布中日两国的客户群体' : '中日両国に広がる顧客層',\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n        </svg>\n      ),\n      color: 'from-green-500 to-green-600'\n    },\n    {\n      number: 50,\n      suffix: '+',\n      label: lang === 'zh' ? '专业团队' : '専門チーム',\n      description: lang === 'zh' ? '经验丰富的物流专家' : '経験豊富な物流専門家',\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\" />\n        </svg>\n      ),\n      color: 'from-purple-500 to-purple-600'\n    },\n    {\n      number: 99,\n      suffix: '%',\n      label: lang === 'zh' ? '客户满意度' : '顧客満足度',\n      description: lang === 'zh' ? '持续提供优质服务' : '継続的に優良サービスを提供',\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n        </svg>\n      ),\n      color: 'from-orange-500 to-red-500'\n    }\n  ]\n\n  return (\n    <section className=\"py-20 bg-gray-50 relative overflow-hidden\">\n      {/* 背景图片 */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <Image\n          src=\"/images/logistics-control-center.webp\"\n          alt=\"Logistics Control Center\"\n          fill\n          className=\"object-cover\"\n        />\n      </div>\n      \n      <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-responsive-lg font-bold text-gray-900 mb-4\">\n            {dict.stats?.title || (lang === 'zh' ? '数据说话' : 'データで語る')}\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            {dict.stats?.subtitle || (lang === 'zh' ? '用数字见证我们的专业实力和服务品质' : '数字で私たちの専門力とサービス品質を証明')}\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {stats.map((stat, index) => (\n            <div\n              key={index}\n              className=\"card p-8 text-center group hover:scale-105 transition-all duration-300\"\n            >\n              <div className={`w-16 h-16 bg-gradient-to-r ${stat.color} rounded-2xl flex items-center justify-center mx-auto mb-6 text-white group-hover:scale-110 transition-transform duration-300`}>\n                {stat.icon}\n              </div>\n              \n              <div className=\"mb-4\">\n                <div className=\"text-4xl font-bold text-gray-900 mb-2\">\n                  <Counter end={stat.number} duration={2000} suffix={stat.suffix} />\n                </div>\n                <h3 className=\"text-lg font-semibold text-gray-800 mb-2\">\n                  {stat.label}\n                </h3>\n                <p className=\"text-sm text-gray-600\">\n                  {stat.description}\n                </p>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* 成就展示 */}\n        <div className=\"mt-20\">\n          <div className=\"text-center mb-12\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              {lang === 'zh' ? '我们的成就' : '私たちの実績'}\n            </h3>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"bg-white rounded-xl p-6 shadow-lg border border-gray-100\">\n              <div className=\"flex items-center mb-4\">\n                <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4\">\n                  <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                </div>\n                <h4 className=\"text-lg font-semibold text-gray-900\">\n                  {lang === 'zh' ? '资质认证' : '資格認証'}\n                </h4>\n              </div>\n              <p className=\"text-gray-600\">\n                {lang === 'zh' ? '获得国际货运代理资质，海关AEO认证企业' : '国際貨物輸送代理資格、税関AEO認証企業を取得'}\n              </p>\n            </div>\n\n            <div className=\"bg-white rounded-xl p-6 shadow-lg border border-gray-100\">\n              <div className=\"flex items-center mb-4\">\n                <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4\">\n                  <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                  </svg>\n                </div>\n                <h4 className=\"text-lg font-semibold text-gray-900\">\n                  {lang === 'zh' ? '服务网络' : 'サービスネットワーク'}\n                </h4>\n              </div>\n              <p className=\"text-gray-600\">\n                {lang === 'zh' ? '覆盖福州-日本主要城市的完整物流网络' : '福州-日本主要都市をカバーする完全な物流ネットワーク'}\n              </p>\n            </div>\n\n            <div className=\"bg-white rounded-xl p-6 shadow-lg border border-gray-100\">\n              <div className=\"flex items-center mb-4\">\n                <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4\">\n                  <svg className=\"w-6 h-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                </div>\n                <h4 className=\"text-lg font-semibold text-gray-900\">\n                  {lang === 'zh' ? '时效保障' : '時間保証'}\n                </h4>\n              </div>\n              <p className=\"text-gray-600\">\n                {lang === 'zh' ? '平均3-5个工作日送达，准时率达99%以上' : '平均3-5営業日で配達、定時率99%以上'}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;;;AAJA;;;AAiBA,SAAS,QAAQ,EAAE,GAAG,EAAE,QAAQ,EAAE,SAAS,EAAE,EAAgB;;IAC3D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI;YACJ,IAAI;YAEJ,MAAM;6CAAU,CAAC;oBACf,IAAI,CAAC,WAAW,YAAY;oBAC5B,MAAM,WAAW,KAAK,GAAG,CAAC,CAAC,cAAc,SAAS,IAAI,UAAU;oBAEhE,SAAS,KAAK,KAAK,CAAC,WAAW;oBAE/B,IAAI,WAAW,GAAG;wBAChB,iBAAiB,sBAAsB;oBACzC;gBACF;;YAEA,iBAAiB,sBAAsB;YAEvC;qCAAO;oBACL,IAAI,gBAAgB;wBAClB,qBAAqB;oBACvB;gBACF;;QACF;4BAAG;QAAC;QAAK;KAAS;IAElB,qBAAO,6LAAC;;YAAM;YAAO;;;;;;;AACvB;GA5BS;KAAA;AA8BM,SAAS,aAAa,EAAE,IAAI,EAAE,IAAI,EAAqB;IACpE,MAAM,QAAQ;QACZ;YACE,QAAQ;YACR,QAAQ;YACR,OAAO,SAAS,OAAO,SAAS;YAChC,aAAa,SAAS,OAAO,aAAa;YAC1C,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;QACT;QACA;YACE,QAAQ;YACR,QAAQ;YACR,OAAO,SAAS,OAAO,SAAS;YAChC,aAAa,SAAS,OAAO,gBAAgB;YAC7C,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;QACT;QACA;YACE,QAAQ;YACR,QAAQ;YACR,OAAO,SAAS,OAAO,SAAS;YAChC,aAAa,SAAS,OAAO,cAAc;YAC3C,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;QACT;QACA;YACE,QAAQ;YACR,QAAQ;YACR,OAAO,SAAS,OAAO,UAAU;YACjC,aAAa,SAAS,OAAO,aAAa;YAC1C,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oBACJ,KAAI;oBACJ,KAAI;oBACJ,IAAI;oBACJ,WAAU;;;;;;;;;;;0BAId,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX,KAAK,KAAK,EAAE,SAAS,CAAC,SAAS,OAAO,SAAS,QAAQ;;;;;;0CAE1D,6LAAC;gCAAE,WAAU;0CACV,KAAK,KAAK,EAAE,YAAY,CAAC,SAAS,OAAO,sBAAsB,sBAAsB;;;;;;;;;;;;kCAI1F,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;wCAAI,WAAW,CAAC,2BAA2B,EAAE,KAAK,KAAK,CAAC,6HAA6H,CAAC;kDACpL,KAAK,IAAI;;;;;;kDAGZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAQ,KAAK,KAAK,MAAM;oDAAE,UAAU;oDAAM,QAAQ,KAAK,MAAM;;;;;;;;;;;0DAEhE,6LAAC;gDAAG,WAAU;0DACX,KAAK,KAAK;;;;;;0DAEb,6LAAC;gDAAE,WAAU;0DACV,KAAK,WAAW;;;;;;;;;;;;;+BAfhB;;;;;;;;;;kCAuBX,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CACX,SAAS,OAAO,UAAU;;;;;;;;;;;0CAI/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAwB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC/E,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,6LAAC;wDAAG,WAAU;kEACX,SAAS,OAAO,SAAS;;;;;;;;;;;;0DAG9B,6LAAC;gDAAE,WAAU;0DACV,SAAS,OAAO,yBAAyB;;;;;;;;;;;;kDAI9C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAyB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAChF,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,6LAAC;wDAAG,WAAU;kEACX,SAAS,OAAO,SAAS;;;;;;;;;;;;0DAG9B,6LAAC;gDAAE,WAAU;0DACV,SAAS,OAAO,uBAAuB;;;;;;;;;;;;kDAI5C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAA0B,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACjF,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,6LAAC;wDAAG,WAAU;kEACX,SAAS,OAAO,SAAS;;;;;;;;;;;;0DAG9B,6LAAC;gDAAE,WAAU;0DACV,SAAS,OAAO,0BAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3D;MAhKwB", "debugId": null}}, {"offset": {"line": 1069, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/official-website/zebra-logistics/src/components/Contact.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Locale } from '@/lib/types'\n\ninterface ContactProps {\n  dict: any\n  lang: Locale\n}\n\nexport default function Contact({ dict, lang }: ContactProps) {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    company: '',\n    message: ''\n  })\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault()\n    // TODO: Implement form submission logic\n    console.log('Form submitted:', formData)\n    alert(lang === 'zh' ? '感谢您的留言，我们会尽快回复！' : 'メッセージありがとうございます。すぐに返信いたします！')\n  }\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    })\n  }\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl font-bold text-gray-900 mb-4\">\n            {dict.contact.title}\n          </h2>\n          <p className=\"text-xl text-gray-600\">\n            {dict.contact.subtitle}\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16\">\n          {/* Contact Information */}\n          <div>\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-8\">\n              {lang === 'zh' ? '联系信息' : '連絡先情報'}\n            </h3>\n            \n            <div className=\"space-y-6\">\n              <div className=\"flex items-start\">\n                <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0\">\n                  <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                  </svg>\n                </div>\n                <div>\n                  <h4 className=\"text-lg font-semibold text-gray-900 mb-1\">\n                    {dict.contact.address}\n                  </h4>\n                  <p className=\"text-gray-600\">\n                    {dict.contact.address_text}\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start\">\n                <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0\">\n                  <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                  </svg>\n                </div>\n                <div>\n                  <h4 className=\"text-lg font-semibold text-gray-900 mb-1\">\n                    {dict.contact.phone}\n                  </h4>\n                  <p className=\"text-gray-600\">\n                    +86 591 8888 8888\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start\">\n                <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0\">\n                  <svg className=\"w-6 h-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                  </svg>\n                </div>\n                <div>\n                  <h4 className=\"text-lg font-semibold text-gray-900 mb-1\">\n                    {dict.contact.email}\n                  </h4>\n                  <p className=\"text-gray-600\">\n                    <EMAIL>\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start\">\n                <div className=\"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0\">\n                  <svg className=\"w-6 h-6 text-yellow-600\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n                  </svg>\n                </div>\n                <div>\n                  <h4 className=\"text-lg font-semibold text-gray-900 mb-1\">\n                    {dict.contact.wechat}\n                  </h4>\n                  <p className=\"text-gray-600\">\n                    zebra_logistics\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Business Hours */}\n            <div className=\"mt-8 bg-white rounded-lg p-6 shadow-md\">\n              <h4 className=\"text-lg font-semibold text-gray-900 mb-4\">\n                {lang === 'zh' ? '营业时间' : '営業時間'}\n              </h4>\n              <div className=\"space-y-2 text-gray-600\">\n                <div className=\"flex justify-between\">\n                  <span>{lang === 'zh' ? '周一至周五' : '月曜日〜金曜日'}</span>\n                  <span>9:00 - 18:00</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span>{lang === 'zh' ? '周六' : '土曜日'}</span>\n                  <span>9:00 - 12:00</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span>{lang === 'zh' ? '周日' : '日曜日'}</span>\n                  <span>{lang === 'zh' ? '休息' : '休み'}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Contact Form */}\n          <div>\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-8\">\n              {lang === 'zh' ? '在线咨询' : 'オンライン相談'}\n            </h3>\n            \n            <form onSubmit={handleSubmit} className=\"bg-white rounded-lg p-8 shadow-md\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n                <div>\n                  <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    {dict.contact.form.name} <span className=\"text-red-500\">*</span>\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"name\"\n                    name=\"name\"\n                    required\n                    value={formData.name}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n                  />\n                </div>\n                \n                <div>\n                  <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    {dict.contact.form.email} <span className=\"text-red-500\">*</span>\n                  </label>\n                  <input\n                    type=\"email\"\n                    id=\"email\"\n                    name=\"email\"\n                    required\n                    value={formData.email}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n                <div>\n                  <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    {dict.contact.form.phone}\n                  </label>\n                  <input\n                    type=\"tel\"\n                    id=\"phone\"\n                    name=\"phone\"\n                    value={formData.phone}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n                  />\n                </div>\n                \n                <div>\n                  <label htmlFor=\"company\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    {dict.contact.form.company}\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"company\"\n                    name=\"company\"\n                    value={formData.company}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"mb-6\">\n                <label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  {dict.contact.form.message} <span className=\"text-red-500\">*</span>\n                </label>\n                <textarea\n                  id=\"message\"\n                  name=\"message\"\n                  rows={5}\n                  required\n                  value={formData.message}\n                  onChange={handleChange}\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors resize-none\"\n                ></textarea>\n              </div>\n\n              <button\n                type=\"submit\"\n                className=\"w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-200\"\n              >\n                {dict.contact.form.submit}\n              </button>\n            </form>\n          </div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAUe,SAAS,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAgB;;IAC1D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,SAAS;IACX;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,wCAAwC;QACxC,QAAQ,GAAG,CAAC,mBAAmB;QAC/B,MAAM,SAAS,OAAO,oBAAoB;IAC5C;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,KAAK,OAAO,CAAC,KAAK;;;;;;sCAErB,6LAAC;4BAAE,WAAU;sCACV,KAAK,OAAO,CAAC,QAAQ;;;;;;;;;;;;8BAI1B,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CACX,SAAS,OAAO,SAAS;;;;;;8CAG5B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;;0EAC/E,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;0EACrE,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;8DAGzE,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEACX,KAAK,OAAO,CAAC,OAAO;;;;;;sEAEvB,6LAAC;4DAAE,WAAU;sEACV,KAAK,OAAO,CAAC,YAAY;;;;;;;;;;;;;;;;;;sDAKhC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAyB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAChF,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEACX,KAAK,OAAO,CAAC,KAAK;;;;;;sEAErB,6LAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAMjC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAA0B,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACjF,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEACX,KAAK,OAAO,CAAC,KAAK;;;;;;sEAErB,6LAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAMjC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAA0B,MAAK;wDAAe,SAAQ;kEACnE,cAAA,6LAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEACX,KAAK,OAAO,CAAC,MAAM;;;;;;sEAEtB,6LAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;8CAQnC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDACX,SAAS,OAAO,SAAS;;;;;;sDAE5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAM,SAAS,OAAO,UAAU;;;;;;sEACjC,6LAAC;sEAAK;;;;;;;;;;;;8DAER,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAM,SAAS,OAAO,OAAO;;;;;;sEAC9B,6LAAC;sEAAK;;;;;;;;;;;;8DAER,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAM,SAAS,OAAO,OAAO;;;;;;sEAC9B,6LAAC;sEAAM,SAAS,OAAO,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOtC,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CACX,SAAS,OAAO,SAAS;;;;;;8CAG5B,6LAAC;oCAAK,UAAU;oCAAc,WAAU;;sDACtC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAO,WAAU;;gEAC7B,KAAK,OAAO,CAAC,IAAI,CAAC,IAAI;gEAAC;8EAAC,6LAAC;oEAAK,WAAU;8EAAe;;;;;;;;;;;;sEAE1D,6LAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,QAAQ;4DACR,OAAO,SAAS,IAAI;4DACpB,UAAU;4DACV,WAAU;;;;;;;;;;;;8DAId,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAQ,WAAU;;gEAC9B,KAAK,OAAO,CAAC,IAAI,CAAC,KAAK;gEAAC;8EAAC,6LAAC;oEAAK,WAAU;8EAAe;;;;;;;;;;;;sEAE3D,6LAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,QAAQ;4DACR,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,WAAU;;;;;;;;;;;;;;;;;;sDAKhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAQ,WAAU;sEAC9B,KAAK,OAAO,CAAC,IAAI,CAAC,KAAK;;;;;;sEAE1B,6LAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,WAAU;;;;;;;;;;;;8DAId,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAU,WAAU;sEAChC,KAAK,OAAO,CAAC,IAAI,CAAC,OAAO;;;;;;sEAE5B,6LAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,OAAO;4DACvB,UAAU;4DACV,WAAU;;;;;;;;;;;;;;;;;;sDAKhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,SAAQ;oDAAU,WAAU;;wDAChC,KAAK,OAAO,CAAC,IAAI,CAAC,OAAO;wDAAC;sEAAC,6LAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAE7D,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAM;oDACN,QAAQ;oDACR,OAAO,SAAS,OAAO;oDACvB,UAAU;oDACV,WAAU;;;;;;;;;;;;sDAId,6LAAC;4CACC,MAAK;4CACL,WAAU;sDAET,KAAK,OAAO,CAAC,IAAI,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzC;GAnOwB;KAAA", "debugId": null}}, {"offset": {"line": 1766, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/official-website/zebra-logistics/src/components/ScrollNavigation.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { Locale } from '@/lib/i18n'\nimport LanguageSwitcher from './LanguageSwitcher'\n\ninterface ScrollNavigationProps {\n  lang: Locale\n  onSectionClick: (sectionIndex: number) => void\n  currentSection: number\n}\n\nexport default function ScrollNavigation({ lang, onSectionClick, currentSection }: ScrollNavigationProps) {\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n  const pathname = usePathname()\n\n  const navItems = [\n    { label: lang === 'zh' ? '首页' : 'ホーム', sectionIndex: 0 },\n    { label: lang === 'zh' ? '服务' : 'サービス', sectionIndex: 1 },\n    { label: lang === 'zh' ? '追踪' : '追跡', sectionIndex: 2 },\n    { label: lang === 'zh' ? '数据' : 'データ', sectionIndex: 3 },\n    { label: lang === 'zh' ? '关于我们' : '会社概要', sectionIndex: 4 },\n    { label: lang === 'zh' ? '联系我们' : 'お問い合わせ', sectionIndex: 5 },\n  ]\n\n  const handleSectionClick = (sectionIndex: number) => {\n    onSectionClick(sectionIndex)\n    setIsMenuOpen(false)\n  }\n\n  return (\n    <nav className=\"bg-white/95 backdrop-blur-sm shadow-lg sticky top-0 z-50 border-b border-gray-100\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href={`/${lang}`} className=\"flex-shrink-0\">\n              <div className=\"flex items-center\">\n                <div className=\"w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg\">\n                  <span className=\"text-white font-bold text-xl\">Z</span>\n                </div>\n                <span className=\"ml-3 text-xl font-bold gradient-text\">\n                  {lang === 'zh' ? '斑马物巢' : 'ゼブラ物流'}\n                </span>\n              </div>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-6\">\n            {navItems.map((item) => (\n              <button\n                key={item.sectionIndex}\n                onClick={() => handleSectionClick(item.sectionIndex)}\n                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${\n                  currentSection === item.sectionIndex\n                    ? 'text-blue-600 bg-blue-50 shadow-sm'\n                    : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50 hover:shadow-sm'\n                }`}\n              >\n                {item.label}\n              </button>\n            ))}\n            \n            {/* 快速功能按钮 */}\n            <div className=\"flex items-center space-x-3 ml-6 pl-6 border-l border-gray-200\">\n              <button \n                onClick={() => handleSectionClick(2)}\n                className=\"btn-secondary px-4 py-2 text-sm font-medium\"\n              >\n                {lang === 'zh' ? '货物追踪' : '貨物追跡'}\n              </button>\n              <button className=\"btn-primary px-4 py-2 text-sm font-medium\">\n                {lang === 'zh' ? '在线报价' : 'オンライン見積'}\n              </button>\n            </div>\n            \n            <LanguageSwitcher currentLang={lang} />\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden flex items-center\">\n            <LanguageSwitcher currentLang={lang} />\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"ml-2 inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100\"\n            >\n              <span className=\"sr-only\">Open main menu</span>\n              <svg\n                className={`${isMenuOpen ? 'hidden' : 'block'} h-6 w-6`}\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n              >\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              </svg>\n              <svg\n                className={`${isMenuOpen ? 'block' : 'hidden'} h-6 w-6`}\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n              >\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      <div className={`md:hidden ${isMenuOpen ? 'block' : 'hidden'}`}>\n        <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200\">\n          {navItems.map((item) => (\n            <button\n              key={item.sectionIndex}\n              onClick={() => handleSectionClick(item.sectionIndex)}\n              className={`block w-full text-left px-3 py-2 rounded-md text-base font-medium transition-colors ${\n                currentSection === item.sectionIndex\n                  ? 'text-blue-600 bg-blue-50'\n                  : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'\n              }`}\n            >\n              {item.label}\n            </button>\n          ))}\n          \n          {/* Mobile 快速功能按钮 */}\n          <div className=\"pt-4 border-t border-gray-200 space-y-2\">\n            <button \n              onClick={() => handleSectionClick(2)}\n              className=\"w-full btn-secondary px-4 py-2 text-sm font-medium\"\n            >\n              {lang === 'zh' ? '货物追踪' : '貨物追跡'}\n            </button>\n            <button className=\"w-full btn-primary px-4 py-2 text-sm font-medium\">\n              {lang === 'zh' ? '在线报价' : 'オンライン見積'}\n            </button>\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;;;AANA;;;;;AAce,SAAS,iBAAiB,EAAE,IAAI,EAAE,cAAc,EAAE,cAAc,EAAyB;;IACtG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW;QACf;YAAE,OAAO,SAAS,OAAO,OAAO;YAAO,cAAc;QAAE;QACvD;YAAE,OAAO,SAAS,OAAO,OAAO;YAAQ,cAAc;QAAE;QACxD;YAAE,OAAO,SAAS,OAAO,OAAO;YAAM,cAAc;QAAE;QACtD;YAAE,OAAO,SAAS,OAAO,OAAO;YAAO,cAAc;QAAE;QACvD;YAAE,OAAO,SAAS,OAAO,SAAS;YAAQ,cAAc;QAAE;QAC1D;YAAE,OAAO,SAAS,OAAO,SAAS;YAAU,cAAc;QAAE;KAC7D;IAED,MAAM,qBAAqB,CAAC;QAC1B,eAAe;QACf,cAAc;IAChB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,CAAC,EAAE,MAAM;gCAAE,WAAU;0CAChC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,6LAAC;4CAAK,WAAU;sDACb,SAAS,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;sCAOlC,6LAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC;wCAEC,SAAS,IAAM,mBAAmB,KAAK,YAAY;wCACnD,WAAW,CAAC,qEAAqE,EAC/E,mBAAmB,KAAK,YAAY,GAChC,uCACA,sEACJ;kDAED,KAAK,KAAK;uCARN,KAAK,YAAY;;;;;8CAa1B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,mBAAmB;4CAClC,WAAU;sDAET,SAAS,OAAO,SAAS;;;;;;sDAE5B,6LAAC;4CAAO,WAAU;sDACf,SAAS,OAAO,SAAS;;;;;;;;;;;;8CAI9B,6LAAC,yIAAA,CAAA,UAAgB;oCAAC,aAAa;;;;;;;;;;;;sCAIjC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yIAAA,CAAA,UAAgB;oCAAC,aAAa;;;;;;8CAC/B,6LAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;;sDAEV,6LAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,6LAAC;4CACC,WAAW,GAAG,aAAa,WAAW,QAAQ,QAAQ,CAAC;4CACvD,OAAM;4CACN,MAAK;4CACL,SAAQ;4CACR,QAAO;sDAEP,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;sDAEvE,6LAAC;4CACC,WAAW,GAAG,aAAa,UAAU,SAAS,QAAQ,CAAC;4CACvD,OAAM;4CACN,MAAK;4CACL,SAAQ;4CACR,QAAO;sDAEP,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/E,6LAAC;gBAAI,WAAW,CAAC,UAAU,EAAE,aAAa,UAAU,UAAU;0BAC5D,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC;gCAEC,SAAS,IAAM,mBAAmB,KAAK,YAAY;gCACnD,WAAW,CAAC,oFAAoF,EAC9F,mBAAmB,KAAK,YAAY,GAChC,6BACA,sDACJ;0CAED,KAAK,KAAK;+BARN,KAAK,YAAY;;;;;sCAa1B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,mBAAmB;oCAClC,WAAU;8CAET,SAAS,OAAO,SAAS;;;;;;8CAE5B,6LAAC;oCAAO,WAAU;8CACf,SAAS,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC;GApIwB;;QAEL,qIAAA,CAAA,cAAW;;;KAFN", "debugId": null}}, {"offset": {"line": 2083, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/code/zebra/official-website/zebra-logistics/src/components/ScrollContainer.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState, useRef, ReactNode } from 'react'\nimport { Locale } from '@/lib/i18n'\nimport ScrollNavigation from './ScrollNavigation'\n\ninterface ScrollContainerProps {\n  children: ReactNode[]\n  sectionIds?: string[]\n  lang: Locale\n}\n\nexport default function ScrollContainer({ children, sectionIds = [], lang }: ScrollContainerProps) {\n  const [currentSection, setCurrentSection] = useState(0)\n  const [isScrolling, setIsScrolling] = useState(false)\n  const containerRef = useRef<HTMLDivElement>(null)\n  const sectionsRef = useRef<HTMLDivElement[]>([])\n  const scrollTimeoutRef = useRef<NodeJS.Timeout>()\n\n  // 滚动到指定区块\n  const scrollToSection = (index: number) => {\n    if (index < 0 || index >= children.length || isScrolling) return\n    \n    const section = sectionsRef.current[index]\n    if (section) {\n      setIsScrolling(true)\n      section.scrollIntoView({ \n        behavior: 'smooth',\n        block: 'start'\n      })\n      \n      // 防止滚动过程中的重复触发\n      if (scrollTimeoutRef.current) {\n        clearTimeout(scrollTimeoutRef.current)\n      }\n      scrollTimeoutRef.current = setTimeout(() => {\n        setIsScrolling(false)\n      }, 1000)\n    }\n  }\n\n  // 监听滚动事件，更新当前区块\n  useEffect(() => {\n    const handleScroll = () => {\n      if (isScrolling) return\n\n      const container = containerRef.current\n      if (!container) return\n\n      const sections = sectionsRef.current\n      const scrollTop = container.scrollTop\n      const containerHeight = container.clientHeight\n\n      // 找到当前最接近顶部的区块\n      let currentIndex = 0\n      let minDistance = Infinity\n\n      sections.forEach((section, index) => {\n        if (section) {\n          const rect = section.getBoundingClientRect()\n          const containerRect = container.getBoundingClientRect()\n          const distance = Math.abs(rect.top - containerRect.top)\n          \n          if (distance < minDistance) {\n            minDistance = distance\n            currentIndex = index\n          }\n        }\n      })\n\n      if (currentIndex !== currentSection) {\n        setCurrentSection(currentIndex)\n      }\n    }\n\n    const container = containerRef.current\n    if (container) {\n      container.addEventListener('scroll', handleScroll, { passive: true })\n      return () => container.removeEventListener('scroll', handleScroll)\n    }\n  }, [currentSection, isScrolling])\n\n  // 键盘导航\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      switch (e.key) {\n        case 'ArrowDown':\n        case 'PageDown':\n          e.preventDefault()\n          scrollToSection(currentSection + 1)\n          break\n        case 'ArrowUp':\n        case 'PageUp':\n          e.preventDefault()\n          scrollToSection(currentSection - 1)\n          break\n        case 'Home':\n          e.preventDefault()\n          scrollToSection(0)\n          break\n        case 'End':\n          e.preventDefault()\n          scrollToSection(children.length - 1)\n          break\n      }\n    }\n\n    window.addEventListener('keydown', handleKeyDown)\n    return () => window.removeEventListener('keydown', handleKeyDown)\n  }, [currentSection, children.length])\n\n  // 鼠标滚轮控制\n  useEffect(() => {\n    const handleWheel = (e: WheelEvent) => {\n      // 如果按住Shift键，使用传统滚动\n      if (e.shiftKey) return\n\n      e.preventDefault()\n      \n      if (isScrolling) return\n\n      if (e.deltaY > 0) {\n        // 向下滚动\n        scrollToSection(currentSection + 1)\n      } else {\n        // 向上滚动\n        scrollToSection(currentSection - 1)\n      }\n    }\n\n    const container = containerRef.current\n    if (container) {\n      container.addEventListener('wheel', handleWheel, { passive: false })\n      return () => container.removeEventListener('wheel', handleWheel)\n    }\n  }, [currentSection, isScrolling])\n\n  return (\n    <div className=\"relative\">\n      {/* 导航栏 */}\n      <ScrollNavigation\n        lang={lang}\n        onSectionClick={scrollToSection}\n        currentSection={currentSection}\n      />\n\n      {/* 滚动容器 */}\n      <div\n        ref={containerRef}\n        className=\"scroll-container h-screen overflow-y-auto\"\n        style={{\n          scrollSnapType: 'y mandatory',\n          scrollBehavior: 'smooth'\n        }}\n      >\n        {children.map((child, index) => (\n          <div\n            key={index}\n            ref={(el) => {\n              if (el) sectionsRef.current[index] = el\n            }}\n            className=\"scroll-section min-h-screen\"\n            style={{\n              scrollSnapAlign: 'start',\n              scrollSnapStop: 'always'\n            }}\n            id={sectionIds[index] || `section-${index}`}\n          >\n            {child}\n          </div>\n        ))}\n      </div>\n\n      {/* 页面指示器 */}\n      <div className=\"fixed right-6 top-1/2 transform -translate-y-1/2 z-50 hidden md:block\">\n        <div className=\"flex flex-col space-y-3\">\n          {children.map((_, index) => (\n            <button\n              key={index}\n              onClick={() => scrollToSection(index)}\n              className={`w-3 h-3 rounded-full border-2 transition-all duration-300 hover:scale-125 ${\n                currentSection === index\n                  ? 'bg-blue-600 border-blue-600 shadow-lg'\n                  : 'bg-transparent border-white/50 hover:border-blue-400'\n              }`}\n              aria-label={`跳转到第${index + 1}个区块`}\n            />\n          ))}\n        </div>\n      </div>\n\n      {/* 滚动提示 */}\n      {currentSection === 0 && (\n        <div className=\"fixed bottom-8 left-1/2 transform -translate-x-1/2 z-40 animate-bounce\">\n          <div className=\"flex flex-col items-center text-white/80\">\n            <span className=\"text-sm mb-2\">向下滚动探索更多</span>\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 14l-7 7m0 0l-7-7m7 7V3\" />\n            </svg>\n          </div>\n        </div>\n      )}\n\n      {/* 传统滚动提示 */}\n      <div className=\"fixed bottom-4 right-4 z-40 text-xs text-gray-500 bg-black/20 backdrop-blur-sm rounded px-2 py-1\">\n        按住 Shift + 滚轮使用传统滚动\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;;;AAJA;;;AAYe,SAAS,gBAAgB,EAAE,QAAQ,EAAE,aAAa,EAAE,EAAE,IAAI,EAAwB;;IAC/F,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB,EAAE;IAC/C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAE9B,UAAU;IACV,MAAM,kBAAkB,CAAC;QACvB,IAAI,QAAQ,KAAK,SAAS,SAAS,MAAM,IAAI,aAAa;QAE1D,MAAM,UAAU,YAAY,OAAO,CAAC,MAAM;QAC1C,IAAI,SAAS;YACX,eAAe;YACf,QAAQ,cAAc,CAAC;gBACrB,UAAU;gBACV,OAAO;YACT;YAEA,eAAe;YACf,IAAI,iBAAiB,OAAO,EAAE;gBAC5B,aAAa,iBAAiB,OAAO;YACvC;YACA,iBAAiB,OAAO,GAAG,WAAW;gBACpC,eAAe;YACjB,GAAG;QACL;IACF;IAEA,gBAAgB;IAChB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;0DAAe;oBACnB,IAAI,aAAa;oBAEjB,MAAM,YAAY,aAAa,OAAO;oBACtC,IAAI,CAAC,WAAW;oBAEhB,MAAM,WAAW,YAAY,OAAO;oBACpC,MAAM,YAAY,UAAU,SAAS;oBACrC,MAAM,kBAAkB,UAAU,YAAY;oBAE9C,eAAe;oBACf,IAAI,eAAe;oBACnB,IAAI,cAAc;oBAElB,SAAS,OAAO;kEAAC,CAAC,SAAS;4BACzB,IAAI,SAAS;gCACX,MAAM,OAAO,QAAQ,qBAAqB;gCAC1C,MAAM,gBAAgB,UAAU,qBAAqB;gCACrD,MAAM,WAAW,KAAK,GAAG,CAAC,KAAK,GAAG,GAAG,cAAc,GAAG;gCAEtD,IAAI,WAAW,aAAa;oCAC1B,cAAc;oCACd,eAAe;gCACjB;4BACF;wBACF;;oBAEA,IAAI,iBAAiB,gBAAgB;wBACnC,kBAAkB;oBACpB;gBACF;;YAEA,MAAM,YAAY,aAAa,OAAO;YACtC,IAAI,WAAW;gBACb,UAAU,gBAAgB,CAAC,UAAU,cAAc;oBAAE,SAAS;gBAAK;gBACnE;iDAAO,IAAM,UAAU,mBAAmB,CAAC,UAAU;;YACvD;QACF;oCAAG;QAAC;QAAgB;KAAY;IAEhC,OAAO;IACP,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;2DAAgB,CAAC;oBACrB,OAAQ,EAAE,GAAG;wBACX,KAAK;wBACL,KAAK;4BACH,EAAE,cAAc;4BAChB,gBAAgB,iBAAiB;4BACjC;wBACF,KAAK;wBACL,KAAK;4BACH,EAAE,cAAc;4BAChB,gBAAgB,iBAAiB;4BACjC;wBACF,KAAK;4BACH,EAAE,cAAc;4BAChB,gBAAgB;4BAChB;wBACF,KAAK;4BACH,EAAE,cAAc;4BAChB,gBAAgB,SAAS,MAAM,GAAG;4BAClC;oBACJ;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;6CAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;oCAAG;QAAC;QAAgB,SAAS,MAAM;KAAC;IAEpC,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;yDAAc,CAAC;oBACnB,oBAAoB;oBACpB,IAAI,EAAE,QAAQ,EAAE;oBAEhB,EAAE,cAAc;oBAEhB,IAAI,aAAa;oBAEjB,IAAI,EAAE,MAAM,GAAG,GAAG;wBAChB,OAAO;wBACP,gBAAgB,iBAAiB;oBACnC,OAAO;wBACL,OAAO;wBACP,gBAAgB,iBAAiB;oBACnC;gBACF;;YAEA,MAAM,YAAY,aAAa,OAAO;YACtC,IAAI,WAAW;gBACb,UAAU,gBAAgB,CAAC,SAAS,aAAa;oBAAE,SAAS;gBAAM;gBAClE;iDAAO,IAAM,UAAU,mBAAmB,CAAC,SAAS;;YACtD;QACF;oCAAG;QAAC;QAAgB;KAAY;IAEhC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,yIAAA,CAAA,UAAgB;gBACf,MAAM;gBACN,gBAAgB;gBAChB,gBAAgB;;;;;;0BAIlB,6LAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;oBACL,gBAAgB;oBAChB,gBAAgB;gBAClB;0BAEC,SAAS,GAAG,CAAC,CAAC,OAAO,sBACpB,6LAAC;wBAEC,KAAK,CAAC;4BACJ,IAAI,IAAI,YAAY,OAAO,CAAC,MAAM,GAAG;wBACvC;wBACA,WAAU;wBACV,OAAO;4BACL,iBAAiB;4BACjB,gBAAgB;wBAClB;wBACA,IAAI,UAAU,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,OAAO;kCAE1C;uBAXI;;;;;;;;;;0BAiBX,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,GAAG,sBAChB,6LAAC;4BAEC,SAAS,IAAM,gBAAgB;4BAC/B,WAAW,CAAC,0EAA0E,EACpF,mBAAmB,QACf,0CACA,wDACJ;4BACF,cAAY,CAAC,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAC;2BAP5B;;;;;;;;;;;;;;;YAcZ,mBAAmB,mBAClB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;sCAAe;;;;;;sCAC/B,6LAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0BAO7E,6LAAC;gBAAI,WAAU;0BAAmG;;;;;;;;;;;;AAKxH;GArMwB;KAAA", "debugId": null}}]}